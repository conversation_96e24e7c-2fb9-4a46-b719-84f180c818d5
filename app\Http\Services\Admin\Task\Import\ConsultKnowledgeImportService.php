<?php

namespace App\Http\Services\Admin\Task\Import;

use App\Http\Consts\ImportConst;
use App\Exceptions\ErrorTrait;
use App\Models\ImportItem;
use App\Models\Article;
use App\Http\Consts\ArticleConst;
use App\Models\Task;
use Illuminate\Support\Facades\DB;

class ConsultKnowledgeImportService
{
    use ErrorTrait;

    protected $task;

    public function import($task)
    {
        $this->task = $task;

        $INSERT_BATCH_SIZE = env('INSERT_BATCH_SIZE', 100);

        $queryBuilder = ImportItem::query()
            ->where('task_id', $task->id)
            ->whereIn('checked', [ImportConst::importItemCheckCheckAccepted])
            ->whereIn('imported', [
                ImportConst::importItemImportedToDo,
                ImportConst::importItemImportedError,
            ]);

        $queryBuilder->clone()
            ->where('type', ImportConst::importItemTypeConsultKnowledge)
            ->chunkById($INSERT_BATCH_SIZE, function ($items) {
                $this->importConsultKnowledge($items);
            });
    }

    private function importConsultKnowledge($items)
    {
        foreach ($items as $item) {
            DB::beginTransaction();
            try {
                $data = $item->data;
                $title = trim($data['title'] ?? '');
                $content = trim($data['content'] ?? '');

                // 基本验证
                if (empty($title)) {
                    throw new \Exception('问题标题不能为空');
                }

                if (empty($content)) {
                    throw new \Exception('答案内容不能为空');
                }

                // 检查是否已存在
                $existingArticle = Article::query()
                    ->where('title', $title)
                    ->where('template', ArticleConst::templateConsultKnowledge)
                    ->first();

                if ($existingArticle) {
                    // 如果已存在，标记为导入成功但不重复创建
                    $item->update([
                        'imported' => ImportConst::importItemImportedDone,
                        'message' => '咨询问答已存在，跳过创建',
                        'imported_at' => now(),
                    ]);
                } else {
                    // 创建咨询问答
                    Article::query()->create([
                        'title' => $title,
                        'template' => ArticleConst::templateConsultKnowledge,
                        'content' => $content,
                        'disabled' => 0,
                    ]);

                    $item->update([
                        'imported' => ImportConst::importItemImportedDone,
                        'message' => '导入成功',
                        'imported_at' => now(),
                    ]);
                }

                DB::commit();
            } catch (\Exception $e) {
                DB::rollBack();
                $item->update([
                    'imported' => ImportConst::importItemImportedError,
                    'message' => '导入失败：' . $e->getMessage(),
                    'imported_at' => now(),
                ]);
            }
        }
    }
}
