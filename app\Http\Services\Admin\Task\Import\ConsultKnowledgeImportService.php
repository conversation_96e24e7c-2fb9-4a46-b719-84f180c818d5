<?php

namespace App\Http\Services\Admin\Task\Import;

use App\Http\Consts\ImportConst;
use App\Exceptions\ErrorTrait;
use App\Models\ImportItem;
use App\Models\Article;
use App\Http\Consts\ArticleConst;

class ConsultKnowledgeImportService
{
    use ErrorTrait;

    protected $task;

    public function import($task)
    {
        $this->task = $task;

        $importItems = ImportItem::query()
            ->where('task_id', $task->id)
            ->where('checked', ImportConst::importItemCheckCheckAccepted)
            ->where('imported', ImportConst::importItemImportedToDo)
            ->get();

        foreach ($importItems as $item) {
            try {
                $this->importItem($item->data);

                $item->update([
                    'imported' => ImportConst::importItemImportedDone,
                    'imported_at' => now(),
                ]);
            } catch (\Exception $e) {
                $item->update([
                    'imported' => ImportConst::importItemImportedError,
                    'message' => $e->getMessage(),
                    'imported_at' => now(),
                ]);
            }
        }
    }

    protected function importItem($data)
    {
        $title = trim($data['title'] ?? '');
        $content = trim($data['content'] ?? '');

        // 检查是否已存在，如果存在则跳过创建（因为检查阶段已经验证过唯一性）
        $existingArticle = Article::query()
            ->where('title', $title)
            ->where('template', ArticleConst::templateConsultKnowledge)
            ->first();

        if ($existingArticle) {
            // 已存在则跳过，不抛出异常
            return;
        }

        // 创建咨询问答
        Article::query()->create([
            'title' => $title,
            'template' => ArticleConst::templateConsultKnowledge,
            'content' => $content,
            'disabled' => 0,
        ]);
    }
}
