<?php

namespace App\Jobs;

use App\Http\Consts\ImportConst;
use App\Http\Consts\MissionPersonConst;
use App\Http\Consts\RegionConst;
use App\Http\Consts\TaskConst;
use App\Http\Services\Admin\Task\Check\MissionInitCheckService;
use App\Http\Services\Admin\Task\Check\MissionManyPersonListCheckService;
use App\Http\Services\Admin\Task\Check\MissionPromoteComparePersonSignCheckService;
use App\Http\Services\Admin\Task\Check\MissionPhysicalCheckResultCheckService;
use App\Http\Services\Admin\Task\Check\MissionPhysicalRecheckResultCheckService;
use App\Http\Services\Admin\Task\Check\MissionPhysicalSpotCheckResultCheckService;
use App\Http\Services\Admin\Task\Check\KnowledgeCheckService;
use App\Http\Services\Admin\Task\Check\QuestionCheckService;
use App\Http\Services\Admin\Task\Check\UserCheckService;
use App\Http\Services\Admin\Task\Check\OutNoticeTaskReceiveCheckService;
use App\Http\Services\Admin\Task\Check\ConsultKnowledgeCheckService;
use App\Http\Services\Admin\Task\Check\RecallTaskSoldierCheckService;
use App\Models\ImportItem;
use App\Models\MissionPerson;
use App\Models\Region;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class TaskCheckJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $task;


    /**
     * Create a new job instance.
     */
    public function __construct($task)
    {
        $this->task = $task;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $task = $this->task;

        switch ($task->type) {
            case TaskConst::taskTypeImportMissionInit:
                $service = new MissionInitCheckService();
                $service->handle($task);
                break;

            case TaskConst::taskTypeImportMissionPromoteComparePersonSignExcel:
                $service = new MissionPromoteComparePersonSignCheckService();
                $service->handle($task);
                break;

            case TaskConst::taskTypeMissionImportMissionIntranetPoliticalPersonList:
            case TaskConst::taskTypeMissionImportMissionIntranetEducationPersonList:
            case TaskConst::taskTypeMissionImportMissionIntranetPreStorePersonList:
            case TaskConst::taskTypeMissionImportMissionIntranetGoPersonStatisticsList:
                $service = new MissionManyPersonListCheckService();
                $service->handle($task);
                break;

            case TaskConst::taskTypeImportMissionPhysicalCheckResult:
                $service = new MissionPhysicalCheckResultCheckService();
                $service->handle($task);
                break;

            case TaskConst::taskTypeImportMissionPhysicalRecheckResult:
                $service = new MissionPhysicalRecheckResultCheckService();
                $service->handle($task);
                break;

            case TaskConst::taskTypeImportMissionPhysicalSpotCheckResult:
                $service = new MissionPhysicalSpotCheckResultCheckService();
                $service->handle($task);
                break;

            case TaskConst::taskTypeImportKnowledgeExcel:
                $service = new KnowledgeCheckService();
                $service->handle($task);
                break;

            case TaskConst::taskTypeImportQuestionExcel:
                $service = new QuestionCheckService();
                $service->handle($task);
                break;

            case TaskConst::taskTypeImportUserExcel:
                $service = new UserCheckService();
                $service->handle($task);
                break;

            case TaskConst::taskTypeImportOutNoticeTaskReceiveExcel:
                $service = new OutNoticeTaskReceiveCheckService();
                $service->handle($task);
                break;

            case TaskConst::taskTypeImportRecallTaskSoldierExcel:
                $service = new RecallTaskSoldierCheckService();
                $service->check($task);
                break;

            case TaskConst::taskTypeImportConsultKnowledgeExcel:
                $service = new ConsultKnowledgeCheckService();
                $service->check($task);
                break;
        }
    }



}
