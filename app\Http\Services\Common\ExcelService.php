<?php

namespace App\Http\Services\Common;

use App\Exceptions\ErrorTrait;
use App\Http\Consts\ArticleConst;
use App\Http\Consts\CategoryConst;
use App\Http\Consts\CourseConst;
use App\Http\Consts\OutNoticeConst;
use App\Http\Consts\QuestionConst;
use App\Http\Consts\RegionConst;
use App\Http\Consts\UserConst;
use App\Http\Traits\ExcelTraits;
use App\Http\Utils\IdCardUtil;
use App\Http\Utils\ScopeQueryImpl;
use App\Http\Utils\ZipRepository;
use App\Models\Article;
use App\Models\Attachment;
use App\Models\Category;
use App\Models\CourseType;
use App\Models\InvitationCode;
use App\Models\OutNoticeTask;
use App\Models\OutNoticeTaskReceive;
use App\Models\Question;
use App\Models\RecallTaskSoldier;
use App\Models\Region;
use App\Models\Role;
use App\Models\Survey;
use App\Models\User;
use App\Models\WorkTask;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class ExcelService
{
    use ExcelTraits;
    use ErrorTrait;

    /**
     * 导出用户
     */
    public function userExport($params, $task)
    {
        $userId  = $task->user_id;
        $user    = User::query()->find($userId);
        $builder = ScopeQueryImpl::adminGetUserQuery($user, $params);
        $items   = $builder->get();
        $headers = [
            '姓名',
            '身份证号',
            '手机号',
            '部门',
            '业务类型',
            '性别',
            '生日',
            '职务',
            '状态',
        ];

        $data = [];
        foreach ($items as $item) {
            $row    = [];
            $row[]  = $item->name;
            $row[]  = $item->id_card . "\t";
            $row[]  = $item->phone . "\t";
            $row[]  = $item->department->name ?? '';
            $row[]  = $item->businessType->name ?? '';
            $row[]  = UserConst::sexMap()[$item->sex] ?? '';
            $row[]  = $item->birthday;
            $row[]  = $item->job;
            $row[]  = UserConst::statusMap()[$item->status] ?? '';
            $data[] = $row;
        }
        $path = storage_path('app/public/excel/');
        if (!file_exists($path)) {
            mkdir($path, 0777, true);
        }
        $exportExcelPath = $path . "用户列表-" . date('YmdHis') . '.xlsx';
        $this->saveExcel('用户列表', $headers, $data, $exportExcelPath);

        $exportExcelUrl = Str::replaceFirst(storage_path('app/public/'), '', $exportExcelPath);
        $task->update([
            'result' => [
                'url' => $exportExcelUrl,
            ],
        ]);
    }

    /**
     * 导入用户（已废弃，使用UserSavingService代替）
     * @deprecated
     */
    public function userImport($params)
    {
        $pinyin = app('pinyin');

        $attachmentId = $params['attachment_id'] ?? '';

        $data = $this->readExcelFormAttachmentId($attachmentId);

        $rows = $data;

        $error = "";

        DB::beginTransaction();
        try {
            foreach ($rows as $index => $row) {
                if ($index == 1) {
                    continue;
                }
                $districtName         = trim($row[1] ?? '');
                $streetName           = trim($row[2] ?? '');
                $communityName        = trim($row[3] ?? '');
                $policeDepartmentName = trim($row[4] ?? '');
                $hospitalName         = trim($row[5] ?? '');
                $schoolName           = trim($row[6] ?? '');
                $companyName          = trim($row[7] ?? '');
                $supervisionName      = trim($row[8] ?? '');
                $otherUnitName        = trim($row[9] ?? '');
                $personName           = trim($row[10] ?? '');
                $jobName              = trim($row[11] ?? '');
                $idCard               = trim($row[12] ?? '');
                $wechatAccount        = trim($row[13] ?? '');
                $phone                = trim($row[14] ?? '');
                $landline             = trim($row[15] ?? '');

                $personName    = Str::replace(' ', '', $personName);
                $communityName = Str::replace($districtName, '', $communityName);
                $department    = null;

                echo $index . "：" . json_encode($row, JSON_UNESCAPED_UNICODE) . "\r\n";

                $district = Region::query()->where("name", $districtName)->where('level', RegionConst::levelDistrict)->first();
                if (!$district) {
                    $error .= "区不存在：" . $districtName . "\r\n";
                }

                $street = Region::query()->where("name", $streetName)->where('level', RegionConst::levelStreet)->first();
                if (!$street && $streetName) {
                    $error .= "街道不存在：" . $streetName . "\r\n";
                }
                $community = Region::query()->where("name", $communityName)->where('level', RegionConst::levelVillage)->first();
                if (!$community && $communityName) {
                    $error .= "社区不存在：" . $communityName . "\r\n";
                }
                if (!User::query()->where("phone", $phone)->exists()) {
                    $roleNames = explode(",", "用户");
                    $roleIds   = [];
                    foreach ($roleNames as $roleName) {
                        $role = Role::query()->where("name", $roleName)->first();
                        if ($role) {
                            $roleIds[] = $role->id;
                        }
                    }
                    $businessTypeName = "其他单位人员";
                    if ($policeDepartmentName) {
                        $businessTypeName = "警务人员";
                    } elseif ($hospitalName) {
                        $businessTypeName = "医务人员";
                    } elseif ($schoolName) {
                        $businessTypeName = "学校征兵人员";
                    } elseif ($companyName) {
                        $businessTypeName = "企业征兵人员";
                    } elseif ($supervisionName) {
                        $businessTypeName = "廉洁监督员";
                    } else {
                        if ($districtName) {
                            $businessTypeName = "区征兵人员";
                        }
                        if ($streetName) {
                            $businessTypeName = "街道征兵人员";
                        }
                        if ($communityName) {
                            $businessTypeName = "社区/村征兵人员";
                        }
                    }
//                    echo json_encode($row, JSON_UNESCAPED_UNICODE) . "\r\n";
                    $businessType = Category::query()->where('template', CategoryConst::templateBusiness)->where("name", $businessTypeName)->first();
                    if (!$businessType) {
                        $businessType = Category::query()->create([
                            'template' => CategoryConst::templateBusiness,
                            'name'     => $businessTypeName,
                            'sort'     => 99,
                            'disabled' => 0,
                        ]);
                    }

                    if ($policeDepartmentName) {
                        $department = Region::query()->where("name", $policeDepartmentName)->where('level', RegionConst::levelPolice)->first();
                        if (!$department) {
                            $error .= "派出所不存在：" . $policeDepartmentName . "\r\n";
                        }
                    }

                    if ($hospitalName) {
                        $department = Region::query()->where("name", $hospitalName)->where('level', RegionConst::levelHospital)->first();
                        if (!$department) {
                            $error .= "医院不存在：" . $hospitalName . "\r\n";
                        }
                    }
                    if ($schoolName) {
                        $department = Region::query()->where("name", $schoolName)->where('level', RegionConst::levelSchool)->first();
                        if (!$department) {
                            $error .= "学校不存在：" . $schoolName . "\r\n";
                        }
                    }
                    if ($companyName) {
                        $department = Region::query()->where("name", $companyName)->where('level', RegionConst::levelCompany)->first();
                        if (!$department) {
                            $code = Str::replace(' ', '', $pinyin->abbr($companyName));
                            while (Region::query()->where('code', $code)->exists()) {
                                $code .= '2';
                            }
                            $department = Region::query()->create([
                                'p_code' => $code,
                                'code'   => Str::uuid(),
                                'name'   => $companyName,
                                'level'  => RegionConst::levelCompany,
                            ]);
                        }
                    }

                    if ($supervisionName) {
                        $department = Region::query()->where("name", $supervisionName)->where('p_code', $district->code)->where('level', RegionConst::levelDepartment)->first();
                        if (!$department) {
                            $code = Str::replace(' ', '', $pinyin->abbr($supervisionName));
                            while (Region::query()->where('code', $code)->exists()) {
                                $code .= '2';
                            }
                            $department = Region::query()->create([
                                'p_code' => $district->code,
                                'code'   => $code,
                                'name'   => $supervisionName,
                                'level'  => RegionConst::levelDepartment,
                            ]);
                        }
                    }

                    if ($otherUnitName) {
                        $department = Region::query()->where("name", $otherUnitName)->where('p_code', $district->code)->where('level', RegionConst::levelDepartment)->first();
                        if (!$department) {
                            $code = Str::replace(' ', '', $pinyin->abbr($otherUnitName));
                            while (Region::query()->where('code', $code)->exists()) {
                                $code .= '2';
                            }
                            $department = Region::query()->create([
                                'p_code' => $district->code,
                                'code'   => $code,
                                'name'   => $otherUnitName,
                                'level'  => RegionConst::levelDepartment,
                            ]);
                        }
                    }

                    if (!isset($department) || !$department) {
                        if ($community) {
                            $department = $community;
                        } else if ($street) {
                            $department = $street;
                        } else if ($district) {
                            $department = $district;
                        }
                    }


                    // 密码手机号后六位
                    $password = substr($idCard, -6);
                    $user     = User::query()->create([
                        "name"             => $personName,
                        "phone"            => $phone,
                        "id_card"          => $idCard,
                        "password"         => Hash::make($password),
                        'department_code'  => $department->code ?? null,
                        'department_name'  => $department->name ?? null,
                        'business_type_id' => $businessType->id ?? null,
                        'job'              => $jobName,
                        'wechat'           => $wechatAccount,
                        'landline'         => $landline,
                        'status'           => UserConst::userStatusNormal,

                        // 导入账号默认激活
                        'activated'        => 1,
                        'activated_at'     => now(),
                    ]);
                    if (is_array($roleIds) && count($roleIds) > 0) {
                        $user->roles()->sync($roleIds);
                    }
                } else {
//                    $error .= "第" . ($index + 1) . "行数据导入失败，手机号{$phone}已存在\n";
                }
            }
            if ($error == "") {
                DB::commit();
            } else {
                DB::rollBack();
                $this->setError($error);
                return false;
            }
        } catch (\Exception $exception) {
            DB::rollBack();
            Log::error($exception);
            $this->setError($exception->getMessage());
            return false;
        }
    }

    public function invitationCodeExport($params, $task)
    {
        $builder = InvitationCode::query();
        $items   = $builder->get();
        $headers = [
            '区',
            '单位',
            '注册码',
        ];
        $data    = [];
        foreach ($items as $item) {
            $row    = [];
            $row[]  = $item->district_name ?? '';
            $row[]  = $item->department_name ?? '';
            $row[]  = $item->code;
            $data[] = $row;
        }
        $path = storage_path('app/public/excel/');
        if (!file_exists($path)) {
            mkdir($path, 0777, true);
        }
        $exportExcelPath = $path . "注册码列表-" . date('YmdHis') . '.xlsx';
        $this->saveExcel('注册码列表', $headers, $data, $exportExcelPath);

        $exportExcelUrl = Str::replaceFirst(storage_path('app/public/'), '', $exportExcelPath);
        $task->update([
            'result' => [
                'url' => $exportExcelUrl,
            ],
        ]);
    }

    /**
     * 导入外呼接收人（已废弃，使用OutNoticeTaskReceiveSavingService代替）
     * @deprecated
     */
    public function outNoticeTaskReceiveImport($params)
    {
        $attachmentId    = $params['attachment_id'] ?? '';
        $outNoticeTaskId = $params['task_id'];

        $data = $this->readExcelFormAttachmentId($attachmentId);

        $rows = $data;

        $error = "";

        DB::beginTransaction();
        try {
            $task    = OutNoticeTask::query()->find($outNoticeTaskId);
            $headers = $rows[1];
            foreach ($rows as $index => $row) {
                if ($index == 1) {
                    continue;
                }

                $rowParams = [];
                foreach ($headers as $headerIndex => $header) {
                    $rowParams[$header] = $row[$headerIndex];
                }
                $name  = trim($row[1] ?? '');
                $phone = trim($row[2] ?? '');
                if ($name == '') {
                    $error .= "第" . ($index + 1) . "行数据导入失败，第1列姓名数据为空\n";
                }
                if ($phone == "") {
                    $error .= "第" . ($index + 1) . "行数据导入失败，第2列手机号数据为空\n";
                }


                $taskType = $task->type;
                if ($taskType == OutNoticeConst::taskTypeSmsVms) {
                    if (!OutNoticeTaskReceive::query()->where([
                        'task_id' => $outNoticeTaskId,
                        'phone'   => $phone,
                        'type'    => OutNoticeConst::taskReceiveTypeSms,
                    ])->exists()) {
                        OutNoticeTaskReceive::query()->create([
                            'params'  => $rowParams,
                            'task_id' => $outNoticeTaskId,
                            'type'    => OutNoticeConst::taskReceiveTypeSms,
                            'name'    => $name,
                            'phone'   => $phone,
                            'status'  => OutNoticeConst::taskReceiveStatusCreated,
                        ]);
                    }
                    if (!OutNoticeTaskReceive::query()->where([
                        'task_id' => $outNoticeTaskId,
                        'phone'   => $phone,
                        'type'    => OutNoticeConst::taskReceiveTypeVms,
                    ])->exists()) {
                        OutNoticeTaskReceive::query()->create([
                            'params'  => $rowParams,
                            'task_id' => $outNoticeTaskId,
                            'type'    => OutNoticeConst::taskReceiveTypeVms,
                            'name'    => $name,
                            'phone'   => $phone,
                            'status'  => OutNoticeConst::taskReceiveStatusCreated,
                        ]);
                    }
                } else {
                    if (!OutNoticeTaskReceive::query()->where([
                        'task_id' => $outNoticeTaskId,
                        'phone'   => $phone,
                        'type'    => OutNoticeConst::taskReceiveTypeSms,
                    ])->exists()) {
                        OutNoticeTaskReceive::query()->create([
                            'params'  => $rowParams,
                            'task_id' => $outNoticeTaskId,
                            'type'    => $taskType,
                            'name'    => $name,
                            'phone'   => $phone,
                            'status'  => OutNoticeConst::taskReceiveStatusCreated,
                        ]);
                    }
                }
            }
            if ($error == "") {
                DB::commit();
            } else {
                DB::rollBack();
                $this->setError($error);
                return false;
            }
            return true;
        } catch (\Exception $exception) {
            DB::rollBack();
            Log::error($exception);
            $this->setError($exception->getMessage());
            return false;
        }
    }

    public function outNoticeTaskReceiveExport($params, $task)
    {
        $outNoticeTaskId = $params['task_id'];

        $builder = OutNoticeTaskReceive::query();
        $builder->where('task_id', $outNoticeTaskId);
        $items   = $builder->get();
        $headers = [
            '姓名',
            '手机号',
            '类型',
            '状态',
        ];
        $data    = [];
        foreach ($items as $item) {
            $row    = [];
            $row[]  = $item->name;
            $row[]  = $item->phone . "\t";
            $row[]  = OutNoticeConst::taskReceiveTypeMap[$item->type] ?? '';
            $row[]  = OutNoticeConst::taskReceiveStatusMap[$item->status] ?? '';
            $data[] = $row;
        }
        $path = storage_path('app/public/excel/');
        if (!file_exists($path)) {
            mkdir($path, 0777, true);
        }
        $exportExcelPath = $path . "外呼接收人列表-" . date('YmdHis') . '.xlsx';
        $this->saveExcel('外呼接收人列表', $headers, $data, $exportExcelPath);

        $exportExcelUrl = Str::replaceFirst(storage_path('app/public/'), '', $exportExcelPath);
        $task->update([
            'result' => [
                'url' => $exportExcelUrl,
            ],
        ]);
    }

    public function teenSoldierImport($params, $task)
    {
        $attachmentId = $params['attachment_id'] ?? '';
        $data         = $this->readExcelFormAttachmentId($attachmentId);
        $rows         = $data;
        foreach ($rows as $index => $row) {
            if ($index == 1) {
                continue;
            }
            $name           = trim($row[1] ?? "");
            $sex            = trim($row[2] ?? '');
            $idCard         = trim($row[3] ?? '');
            $nation         = trim($row[4] ?? '');
            $political      = trim($row[5] ?? '');
            $districtName   = trim($row[6] ?? '');
            $from           = trim($row[7] ?? '');
            $phone          = trim($row[8] ?? '');
            $education      = trim($row[9] ?? '');
            $major          = trim($row[10] ?? '');
            $studySituation = trim($row[11] ?? '');
            $majorCategory  = trim($row[12] ?? '');
            $studyMajor     = trim($row[13] ?? '');
            $certificate    = trim($row[14] ?? '');

            $district = Region::query()->where([
                'name'  => $districtName,
                'level' => RegionConst::levelDistrict,
            ])->first();

            $idCardUtil = new IdCardUtil($idCard);
            $age        = $idCardUtil->getAge();

            TeenSoldier::query()->create([
                'name'                 => $name,
                'sex'                  => $sex,
                'age'                  => $age,
                'id_card'              => $idCard,
                'nation'               => $nation,
                'political'            => $political,
                'district_name'        => $districtName,
                'district_code'        => $district->code ?? null,
                'department_tree_code' => $district ? $district->tree_code : null,
                'from'                 => $from,
                'phone'                => $phone,
                'education'            => $education,
                'major'                => $major,
                'study_situation'      => $studySituation,
                'major_category'       => $majorCategory,
                'study_major'          => $studyMajor,
                'certificate'          => $certificate,
            ]);
        }
    }

    public function teenSoldierExport($params, $task)
    {
        $userId  = $task->user_id;
        $user    = User::query()->find($userId);
        $builder = ScopeQueryImpl::adminGetTeenSoldierQuery($user, $params);
        $items   = $builder->get();
        $headers = [
            '姓名',
            '性别',
            '身份证号',
            '民族',
            '政治面貌',
            '所在区县',
            '户籍地',
            '手机号',
            '学历',
            '专业',
            '学习情况',
            '专业类别',
            '学习专业',
            '证书',
        ];
        $data    = [];
        foreach ($items as $item) {
            $row    = [];
            $row[]  = $item->name;
            $row[]  = $item->sex;
            $row[]  = $item->id_card . "\t";
            $row[]  = $item->nation;
            $row[]  = $item->political;
            $row[]  = $item->district_name;
            $row[]  = $item->from;
            $row[]  = $item->phone . "\t";
            $row[]  = $item->education;
            $row[]  = $item->major;
            $row[]  = $item->study_situation;
            $row[]  = $item->major_category;
            $row[]  = $item->study_major;
            $row[]  = $item->certificate;
            $data[] = $row;
        }
        $path = storage_path('app/public/excel/');
        if (!file_exists($path)) {
            mkdir($path, 0777, true);
        }
        $exportExcelPath = $path . "应征青年列表-" . date('YmdHis') . '.xlsx';
        $this->saveExcel('应征青年列表', $headers, $data, $exportExcelPath);
        $exportExcelUrl = Str::replaceFirst(storage_path('app/public/'), '', $exportExcelPath);
        $task->update([
            'result' => [
                'url' => $exportExcelUrl,
            ],
        ]);
    }

    public function enlistSoldierImport($params, $task)
    {
        $attachmentId = $params['attachment_id'] ?? '';
        $data         = $this->readExcelFormAttachmentId($attachmentId);
        $rows         = $data;
        foreach ($rows as $index => $row) {
            if ($index == 1) {
                continue;
            }
            $name           = trim($row[1] ?? '');
            $sex            = trim($row[2] ?? '');
            $idCard         = trim($row[3] ?? '');
            $nation         = trim($row[4] ?? '');
            $political      = trim($row[5] ?? '');
            $districtName   = trim($row[6] ?? '');
            $school         = trim($row[7] ?? '');
            $education      = trim($row[8] ?? '');
            $major          = trim($row[9] ?? '');
            $studySituation = trim($row[10] ?? '');
            $majorCategory  = trim($row[11] ?? '');
            $studyMajor     = trim($row[12] ?? '');
            $certificate    = trim($row[13] ?? '');
            $enlistUnit     = trim($row[14] ?? '');
            $approveUnit    = trim($row[15] ?? '');

            $district = Region::query()->where([
                'name'  => $districtName,
                'level' => RegionConst::levelDistrict,
            ])->first();
            EnlistSoldier::query()->create([
                'name'                 => $name,
                'sex'                  => $sex,
                'id_card'              => $idCard,
                'nation'               => $nation,
                'political'            => $political,
                'district_name'        => $districtName,
                'district_code'        => $district->code ?? null,
                'department_tree_code' => $district ? $district->tree_code : null,
                'school'               => $school,
                'education'            => $education,
                'major'                => $major,
                'study_situation'      => $studySituation,
                'major_category'       => $majorCategory,
                'study_major'          => $studyMajor,
                'certificate'          => $certificate,
                'enlist_unit'          => $enlistUnit,
                'approved_unit'        => $approveUnit,
            ]);
        }
    }

    public function enlistSoldierExport($params, $task)
    {
        $userId  = $task->user_id;
        $user    = User::query()->find($userId);
        $builder = ScopeQueryImpl::adminGetEnlistSoldierQuery($user, $params);
        $items   = $builder->get();
        $headers = [
            '姓名',
            '性别',
            '身份证号',
            '民族',
            '政治面貌',
            '所在区县',
            '毕业或所在院校',
            '文化程度',
            '所学专业',
            '学业情况',
            '学习类型',
            '专业类别',
            '证书',
            '入伍单位',
            '批准入伍机关',
        ];
        $data    = [];
        foreach ($items as $item) {
            $row    = [];
            $row[]  = $item->name;
            $row[]  = $item->sex;
            $row[]  = $item->id_card . "\t";
            $row[]  = $item->nation;
            $row[]  = $item->political;
            $row[]  = $item->district_name;
            $row[]  = $item->school;
            $row[]  = $item->education;
            $row[]  = $item->major;
            $row[]  = $item->study_situation;
            $row[]  = $item->major_category;
            $row[]  = $item->study_major;
            $row[]  = $item->certificate;
            $row[]  = $item->enlist_unit;
            $row[]  = $item->approved_unit;
            $data[] = $row;
        }
        $path = storage_path('app/public/excel/');
        if (!file_exists($path)) {
            mkdir($path, 0777, true);
        }
        $exportExcelPath = $path . "在伍士兵列表-" . date('YmdHis') . '.xlsx';
        $this->saveExcel('在伍士兵列表', $headers, $data, $exportExcelPath);
        $exportExcelUrl = Str::replaceFirst(storage_path('app/public/'), '', $exportExcelPath);
        $task->update([
            'result' => [
                'url' => $exportExcelUrl,
            ],
        ]);
    }

    public function exSoldierImport($params, $task)
    {
        $attachmentId = $params['attachment_id'] ?? '';
        $data         = $this->readExcelFormAttachmentId($attachmentId);
        $rows         = $data;
        $error        = "";
        DB::beginTransaction();
        foreach ($rows as $index => $row) {
            if ($index == 1) {
                continue;
            }

            Log::info($row);
            $name                   = trim($row[1] ?? '');
            $sex                    = trim($row[2] ?? '');
            $idCard                 = trim($row[3] ?? '');
            $enlistmentTime         = trim($row[4] ?? '');
            $enlistmentDistrictName = trim($row[5] ?? '');
            $enlistmentStreetName   = trim($row[6] ?? '');
            $education              = trim($row[7] ?? '');
            $majorType              = trim($row[8] ?? '');
            $political              = trim($row[9] ?? '');
            $serviceUnit            = trim($row[10] ?? '');
            $serviceUnitSecondary   = trim($row[11] ?? '');
            $serviceJob             = trim($row[12] ?? '');
            $serviceJobCategory     = trim($row[13] ?? '');
            $serviceBackbone        = trim($row[14] ?? '');
            $retirementTime         = trim($row[15] ?? '');
            $retirementRank         = trim($row[16] ?? '');
            $enlistmentProvince     = trim($row[17] ?? '');
            $familyAddress          = trim($row[18] ?? '');
            $nowWorkingUnit         = trim($row[19] ?? '');
            $nowMajor               = trim($row[20] ?? '');
            $contact                = trim($row[21] ?? '');
            $familyContact          = trim($row[22] ?? '');
            $married                = trim($row[23] ?? '');
            $single                 = trim($row[24] ?? '');
            $birth                  = trim($row[25] ?? '');
            $district               = Region::query()->where([
                'name'  => $enlistmentDistrictName,
                'level' => RegionConst::levelDistrict,
            ])->first();

            try {
                $enlistmentTimeCarbon = Carbon::parse($enlistmentTime);
            } catch (Exception $e) {
                $error                .= "第{$index}行，入伍时间格式错误，正确示例：2020-01-01\n";
                $enlistmentTimeCarbon = null;
            }
            try {
                $retirementTimeCarbon = Carbon::parse($retirementTime);
            } catch (Exception $e) {
                $error                .= "第{$index}行，退伍时间格式错误，正确示例：2020-01-01\n";
                $retirementTimeCarbon = null;
            }

            ExSoldier::query()->create([
                'name'                     => $name,
                'sex'                      => $sex,
                'id_card'                  => $idCard,
                'enlistment_time'          => $enlistmentTimeCarbon ?: '',
                'enlistment_district_name' => $enlistmentDistrictName,
                'enlistment_district_code' => $district->code ?? null,
                'enlistment_street_name'   => $enlistmentStreetName,
                'department_tree_code'     => $district ? $district->tree_code : null,
                'education'                => $education,
                'major_type'               => $majorType,
                'political'                => $political,
                'service_unit'             => $serviceUnit,
                'service_unit_secondary'   => $serviceUnitSecondary,
                'service_job'              => $serviceJob,
                'service_job_category'     => $serviceJobCategory,
                'service_backbone'         => $serviceBackbone,
                'retirement_time'          => $retirementTimeCarbon ?: '',
                'retirement_rank'          => $retirementRank,
                'enlistment_province'      => $enlistmentProvince,
                'family_address'           => $familyAddress,
                'now_working_unit'         => $nowWorkingUnit,
                'now_major'                => $nowMajor,
                'contact'                  => $contact,
                'family_contact'           => $familyContact,
                'married'                  => $married,
                'single'                   => $single,
                'birth'                    => $birth,
            ]);
        }

        if ($error == "") {
            DB::commit();
        } else {
            DB::rollBack();
            $this->setError($error);
            return false;
        }
    }

    public function exSoldierExport($params, $task)
    {
        $userId  = $task->user_id;
        $user    = User::query()->find($userId);
        $builder = ScopeQueryImpl::adminGetExSoldierQuery($user, $params);
        $items   = $builder->get();
        $headers = [
            '姓名',
            '性别',
            '身份证号',
            '入伍时间',
            '入伍区县',
            '入伍街道（院校）',
            '文化程度',
            '专业类别',
            '政治面貌',
            '服现役大单位',
            '服现役旅营级单位',
            '专业岗位',
            '专业类别',
            '任骨干情况',
            '退伍时间',
            '退伍时军衔',
            '入伍前省份',
            '家庭住址',
            '现工作单位及职务（已就业人员填写）',
            '现从事专业（或大学、高级工专业）',
            '联系方式',
            '家庭成员联系方式',
            '是否结婚',
            '是否独生子女',
            '是否生育',
        ];
        $data    = [];
        foreach ($items as $item) {
            $row    = [];
            $row[]  = $item->name;
            $row[]  = $item->sex;
            $row[]  = $item->id_card . "\t";
            $row[]  = $item->enlistment_time;
            $row[]  = $item->enlistment_district_name;
            $row[]  = $item->enlistment_street_name;
            $row[]  = $item->education;
            $row[]  = $item->major_type;
            $row[]  = $item->political;
            $row[]  = $item->service_unit;
            $row[]  = $item->service_unit_secondary;
            $row[]  = $item->service_job;
            $row[]  = $item->service_job_category;
            $row[]  = $item->service_backbone;
            $row[]  = $item->retirement_time;
            $row[]  = $item->retirement_rank;
            $row[]  = $item->enlistment_province;
            $row[]  = $item->family_address;
            $row[]  = $item->now_working_unit;
            $row[]  = $item->now_major;
            $row[]  = $item->contact . "\t";
            $row[]  = $item->family_contact . "\t";
            $row[]  = $item->married;
            $row[]  = $item->single;
            $row[]  = $item->birth;
            $data[] = $row;
        }
        $path = storage_path('app/public/excel/');
        if (!file_exists($path)) {
            mkdir($path, 0777, true);
        }
        $exportExcelPath = $path . "退役士兵列表-" . date('YmdHis') . '.xlsx';
        $this->saveExcel('退役士兵列表', $headers, $data, $exportExcelPath);
        $exportExcelUrl = Str::replaceFirst(storage_path('app/public/'), '', $exportExcelPath);
        $task->update([
            'result' => [
                'url' => $exportExcelUrl,
            ],
        ]);
    }

    /**
     * 导入特殊时期征召人员（已废弃，使用RecallTaskSoldierSavingService代替）
     * @deprecated
     */
    public function recallTaskSoldierImport($params, $task)
    {
        $attachmentId = $params['attachment_id'] ?? null;
        $recallTaskId = $params['task_id'] ?? null;
        $data         = $this->readExcelFormAttachmentId($attachmentId);
        $rows         = $data;
        $error        = "";
        DB::beginTransaction();
        foreach ($rows as $index => $row) {
            if ($index == 1) {
                continue;
            }

            Log::info($row);
            $name                   = trim($row[1] ?? '');
            $sex                    = trim($row[2] ?? '');
            $idCard                 = trim($row[3] ?? '');
            $enlistmentTime         = trim($row[4] ?? '');
            $enlistmentDistrictName = trim($row[5] ?? '');
            $enlistmentStreetName   = trim($row[6] ?? '');
            $education              = trim($row[7] ?? '');
            $majorType              = trim($row[8] ?? '');
            $political              = trim($row[9] ?? '');
            $serviceUnit            = trim($row[10] ?? '');
            $serviceUnitSecondary   = trim($row[11] ?? '');
            $serviceJob             = trim($row[12] ?? '');
            $serviceJobCategory     = trim($row[13] ?? '');
            $serviceBackbone        = trim($row[14] ?? '');
            $retirementTime         = trim($row[15] ?? '');
            $retirementRank         = trim($row[16] ?? '');
            $enlistmentProvince     = trim($row[17] ?? '');
            $familyAddress          = trim($row[18] ?? '');
            $nowWorkingUnit         = trim($row[19] ?? '');
            $nowMajor               = trim($row[20] ?? '');
            $contact                = trim($row[21] ?? '');
            $familyContact          = trim($row[22] ?? '');
            $married                = trim($row[23] ?? '');
            $single                 = trim($row[24] ?? '');
            $birth                  = trim($row[25] ?? '');
            $district               = Region::query()->where([
                'name'  => $enlistmentDistrictName,
                'level' => RegionConst::levelDistrict,
            ])->first();

            try {
                $enlistmentTimeCarbon = Carbon::parse($enlistmentTime);
            } catch (Exception $e) {
                $error                .= "第{$index}行，入伍时间格式错误，正确示例：2020-01-01\n";
                $enlistmentTimeCarbon = null;
            }
            try {
                $retirementTimeCarbon = Carbon::parse($retirementTime);
            } catch (Exception $e) {
                $error                .= "第{$index}行，退伍时间格式错误，正确示例：2020-01-01\n";
                $retirementTimeCarbon = null;
            }

            RecallTaskSoldier::query()->create([
                'task_id'                  => $recallTaskId,
                'name'                     => trim($name),
                'sex'                      => $sex,
                'id_card'                  => trim($idCard),
                'enlistment_time'          => $enlistmentTimeCarbon ?: '',
                'enlistment_district_name' => $enlistmentDistrictName,
                'enlistment_district_code' => $district->code ?? null,
                'enlistment_street_name'   => $enlistmentStreetName,
                'department_tree_code'     => $district ? $district->tree_code : null,
                'education'                => $education,
                'major_type'               => $majorType,
                'political'                => $political,
                'service_unit'             => $serviceUnit,
                'service_unit_secondary'   => $serviceUnitSecondary,
                'service_job'              => $serviceJob,
                'service_job_category'     => $serviceJobCategory,
                'service_backbone'         => $serviceBackbone,
                'retirement_time'          => $retirementTime ?: '',
                'retirement_rank'          => $retirementRank,
                'enlistment_province'      => $enlistmentProvince,
                'family_address'           => $familyAddress,
                'now_working_unit'         => $nowWorkingUnit,
                'now_major'                => $nowMajor,
                'contact'                  => trim($contact),
                'family_contact'           => $familyContact,
                'married'                  => $married,
                'single'                   => $single,
                'birth'                    => $birth,
            ]);
        }

        if ($error == "") {
            DB::commit();
        } else {
            DB::rollBack();
            $this->setError($error);
            return false;
        }
    }

    public function recallTaskSoldierLinkExport($params, $task)
    {
        $userId  = $task->user_id;
        $user    = User::query()->find($userId);
        $builder = ScopeQueryImpl::adminGetRecallTaskSoldierQuery($user, $params);
        $builder->orderBy("created_at", "desc");
        $items   = $builder->get();
        $headers = [
            '姓名',
            '联系方式',
            'link',
        ];
        $data    = [];
        foreach ($items as $item) {
            $uniLink = get_random_str(8);
            while (RecallTaskSoldier::query()->where('uni_link', $uniLink)->exists()) {
                $uniLink = get_random_str(8);
            }
            $item->update([
                'wx_link'  => $item->getMiniUrlLinkParam(),
                'uni_link' => $uniLink,
            ]);

            $row    = [];
            $row[]  = $item->name;
            $row[]  = $item->contact . "\t";
            $row[]  = $item->uni_link;
            $data[] = $row;
            usleep(20 * 1000);
        }
        $path = storage_path('app/public/excel/');
        if (!file_exists($path)) {
            mkdir($path, 0777, true);
        }
        $exportExcelPath = $path . "特殊时期征召人员链接列表-" . date('YmdHis') . '.xlsx';
        $this->saveExcel('特殊时期征召人员链接列表', $headers, $data, $exportExcelPath);
        $exportExcelUrl = Str::replaceFirst(storage_path('app/public/'), '', $exportExcelPath);
        $task->update([
            'result' => [
                'url' => $exportExcelUrl,
            ],
        ]);
    }


    public function recallTaskSoldierExport($params, $task)
    {
        $userId  = $task->user_id;
        $user    = User::query()->find($userId);
        $builder = ScopeQueryImpl::adminGetRecallTaskSoldierQuery($user, $params);
        $builder->orderBy("created_at", "desc");
        $items   = $builder->get();
        $headers = [
            '姓名',
            '性别',
            '身份证号',
            '入伍时间',
            '入伍区县',
            '入伍街道（院校）',
            '文化程度',
            '专业类别',
            '政治面貌',
            '服现役大单位',
            '服现役旅营级单位',
            '专业岗位',
            '专业类别',
            '任骨干情况',
            '退伍时间',
            '退伍时军衔',
            '入伍前省份',
            '家庭住址',
            '现工作单位及职务（已就业人员填写）',
            '现从事专业（或大学、高级工专业）',
            '联系方式',
            '家庭成员联系方式',
            '是否结婚',
            '是否独生子女',
            '是否生育',

            '人脸识别是否完成',
            '人脸识别完成时间',
            '体检是否完成',
            '体检完成时间',
            '政考是否完成',
            '政考完成时间',
            '起运是否完成',
            '起运完成时间',
        ];
        $data    = [];
        foreach ($items as $item) {
            $row   = [];
            $row[] = $item->name;
            $row[] = $item->sex;
            $row[] = $item->id_card . "\t";
            $row[] = $item->enlistment_time;
            $row[] = $item->enlistment_district_name;
            $row[] = $item->enlistment_street_name;
            $row[] = $item->education;
            $row[] = $item->major_type;
            $row[] = $item->political;
            $row[] = $item->service_unit;
            $row[] = $item->service_unit_secondary;
            $row[] = $item->service_job;
            $row[] = $item->service_job_category;
            $row[] = $item->service_backbone;
            $row[] = $item->retirement_time;
            $row[] = $item->retirement_rank;
            $row[] = $item->enlistment_province;
            $row[] = $item->family_address;
            $row[] = $item->now_working_unit;
            $row[] = $item->now_major;
            $row[] = $item->contact . "\t";
            $row[] = $item->family_contact . "\t";
            $row[] = $item->married;
            $row[] = $item->single;
            $row[] = $item->birth;

            $row[]  = $item->face_complete ? '是' : '否';
            $row[]  = $item->face_complete_at;
            $row[]  = $item->body_examination_complete ? '是' : '否';
            $row[]  = $item->body_examination_complete_at;
            $row[]  = $item->political_examination_complete ? '是' : '否';
            $row[]  = $item->political_examination_complete_at;
            $row[]  = $item->transport_complete ? '是' : '否';
            $row[]  = $item->transport_complete_at;
            $data[] = $row;
        }
        $path = storage_path('app/public/excel/');
        if (!file_exists($path)) {
            mkdir($path, 0777, true);
        }
        $exportExcelPath = $path . "特殊时期征召人员列表-" . date('YmdHis') . '.xlsx';
        $this->saveExcel('特殊时期征召人员列表', $headers, $data, $exportExcelPath);
        $exportExcelUrl = Str::replaceFirst(storage_path('app/public/'), '', $exportExcelPath);
        $task->update([
            'result' => [
                'url' => $exportExcelUrl,
            ],
        ]);
    }

    public function questionExport($params, $task)
    {
        $questions = Question::query()->get();
        $headers   = [
            '题目', '题型', '知识点', '课程类型', '要点提示', '正确答案', '选项A', '选项B', '选项C', '选项D',
        ];
        $data      = [];
        foreach ($questions as $question) {
            $knowledges    = $question->knowledges()->get()->toArray();
            $knowledgesStr = implode(',', array_column($knowledges, 'name'));
            $courseType    = $question->courseType()->first();
            $answers       = $question->answers()->get()->toArray();
            $rightAnswer   = "";
            foreach ($answers as $index => $answer) {
                $rightAnswer .= chr(ord('A') + $index);
            }
            $row   = [];
            $row[] = $question->title;
            $row[] = QuestionConst::typeMap[$question->type] ?? "";
            $row[] = $knowledgesStr;
            $row[] = $courseType->name ?? "";
            $row[] = $question->analysis;
            $row[] = $rightAnswer;
            foreach ($answers as $answer) {
                $row[] = $answer['title'];
            }
            $data[] = $row;
        }
        $path = storage_path('app/public/excel/');
        if (!file_exists($path)) {
            mkdir($path, 0777, true);
        }
        $exportExcelPath = $path . "题库列表-" . date('YmdHis') . '.xlsx';
        $this->saveExcel('题库列表', $headers, $data, $exportExcelPath);
        $exportExcelUrl = Str::replaceFirst(storage_path('app/public/'), '', $exportExcelPath);
        $task->update([
            'result' => [
                'url' => $exportExcelUrl,
            ],
        ]);
    }

    /**
     * 导入题目（已废弃，使用QuestionSavingService代替）
     * @deprecated
     */
    public function questionImport($params, $task)
    {
        $attachmentId = $params['attachment_id'] ?? '';
        $data         = $this->readExcelFormAttachmentId($attachmentId);
        $rows         = $data;
        $error        = "";
        DB::beginTransaction();
        try {
            foreach ($rows as $index => $row) {
                if ($index == 1) {
                    continue;
                }
                $titleStr       = trim($row[1] ?? '');
                $typeStr        = trim($row[2] ?? '');
                $knowledgeStr   = trim($row[3] ?? '');
                $courseTypeStr  = trim($row[4] ?? '');
                $analysisStr    = trim($row[5] ?? '');
                $rightAnswerStr = trim($row[6] ?? '');
                $answerIndex    = 7;
                $answerArr      = [];
                while (isset($row[$answerIndex]) && $row[$answerIndex] != "") {
                    $answerArr[] = $row[$answerIndex];
                    $answerIndex++;
                }
                $type = array_flip(QuestionConst::typeMap)[$typeStr] ?? '';
                if ($type == "") {
                    $error .= "第" . ($index + 1) . "行题型错误";
                }
                $knowledgeArr = explode(',', $knowledgeStr);

                $courseTypeId = CourseType::query()->firstOrCreate([
                    'name'     => $courseTypeStr,
                    'template' => CategoryConst::templateCourse,
                ], [
                    'name'     => $courseTypeStr,
                    'template' => CategoryConst::templateCourse,
                ])->id;

                $question = Question::query()->create([
                    'course_type_id' => $courseTypeId,
                    'type'           => $type,
                    'title'          => $titleStr,
                    'analysis'       => $analysisStr,
                ]);

                $knowledgeIdArr = [];
                foreach ($knowledgeArr as $knowledgeTempStr) {
                    $knowledge        = Category::query()->firstOrCreate([
                        'name'     => $knowledgeTempStr,
                        'template' => CategoryConst::templateKnowledge,
                    ], [
                        'name'     => $knowledgeTempStr,
                        'template' => CategoryConst::templateKnowledge,
                    ]);
                    $knowledgeIdArr[] = $knowledge->id;
                }

//                Log::info($knowledgeStr);
//                Log::info($knowledgeArr);
//                Log::info($knowledgeIdArr);

                $question->knowledges()->sync($knowledgeIdArr);

                foreach ($answerArr as $answerIndex => $answerTempStr) {
                    $question->answers()->create([
                        'is_correct' => str_contains($rightAnswerStr, chr(ord('A') + $answerIndex)),
                        'title'      => $answerTempStr,
                    ]);
                }
            }
            if ($error == "") {
                DB::commit();
            } else {
                DB::rollBack();
                $this->setError($error);
                return false;
            }
            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();
            Log::error($exception);
            $this->setError($exception->getMessage());
            return false;
        }
    }

    /**
     * 导入知识点（已废弃，使用KnowledgeSavingService代替）
     * @deprecated
     */
    public function knowledgeImport($params, $task)
    {
        $attachmentId = $params['attachment_id'] ?? '';
        $data         = $this->readExcelFormAttachmentId($attachmentId);
        $rows         = $data;
        $error        = "";
        DB::beginTransaction();
        try {
            foreach ($rows as $index => $row) {
                if ($index == 1) {
                    continue;
                }
                $knowledgeStr = trim($row[1] ?? '');
                $knowledge    = Category::query()->firstOrCreate([
                    'name'     => $knowledgeStr,
                    'template' => CategoryConst::templateKnowledge,
                ], [
                    'name'     => $knowledgeStr,
                    'template' => CategoryConst::templateKnowledge,
                ]);
            }
            if ($error == "") {
                DB::commit();
            } else {
                DB::rollBack();
                $this->setError($error);
                return false;
            }
            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();
            Log::error($exception);
            $this->setError($exception->getMessage());
            return false;
        }
    }

    public function knowledgeExport($params, $task)
    {
        $knowledges = Category::query()->where('template', CategoryConst::templateKnowledge)
            ->orderByDesc("created_at", "desc")
            ->get();
        $headers    = [
            '知识点名称',
        ];
        $data       = [];
        foreach ($knowledges as $knowledge) {
            $row    = [];
            $row[]  = $knowledge->name;
            $data[] = $row;
        }
        $path = storage_path('app/public/excel/');
        if (!file_exists($path)) {
            mkdir($path, 0777, true);
        }
        $exportExcelPath = $path . "知识点列表-" . date('YmdHis') . '.xlsx';
        $this->saveExcel('知识点列表', $headers, $data, $exportExcelPath);
        $exportExcelUrl = Str::replaceFirst(storage_path('app/public/'), '', $exportExcelPath);
        $task->update([
            'result' => [
                'url' => $exportExcelUrl,
            ],
        ]);
    }

    /**
     * 导入咨询问答（已废弃，使用ConsultKnowledgeSavingService代替）
     * @deprecated
     */
    public function consultKnowledgeImport($params, $task)
    {
        $attachmentId = $params['attachment_id'] ?? '';
        $data         = $this->readExcelFormAttachmentId($attachmentId);
        $rows         = $data;
        $error        = "";
        DB::beginTransaction();
        try {
            foreach ($rows as $index => $row) {
                if ($index == 1) {
                    continue;
                }
                $titleStr   = trim($row[1] ?? '');
                $contentStr = trim($row[2] ?? '');
                $article    = Article::query()->firstOrCreate([
                    'title'    => $titleStr,
                    'template' => ArticleConst::templateConsultKnowledge,
                ], [
                    'title'    => $titleStr,
                    'template' => ArticleConst::templateConsultKnowledge,
                    'content'  => $contentStr,
                    'disabled' => 0,
                ]);
            }
            if ($error == "") {
                DB::commit();
            } else {
                DB::rollBack();
                $this->setError($error);
                return false;
            }
            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();
            Log::error($exception);
            $this->setError($exception->getMessage());
            return false;
        }
    }

    public function consultKnowledgeExport($params, $task)
    {
        $consultKnowledges = Article::query()->where('template', ArticleConst::templateConsultKnowledge)->orderByDesc("created_at", "desc")->get();
        $headers           = [
            '问题',
            '答案'
        ];
        $data              = [];
        foreach ($consultKnowledges as $consultKnowledge) {
            $row    = [];
            $row[]  = $consultKnowledge->title;
            $row[]  = strip_tags($consultKnowledge->content);
            $data[] = $row;
        }
        $path = storage_path('app/public/excel/');
        if (!file_exists($path)) {
            mkdir($path, 0777, true);
        }
        $exportExcelPath = $path . "咨询知识列表-" . date('YmdHis') . '.xlsx';
        $this->saveExcel('咨询知识列表', $headers, $data, $exportExcelPath);
        $exportExcelUrl = Str::replaceFirst(storage_path('app/public/'), '', $exportExcelPath);
        $task->update([
            'result' => [
                'url' => $exportExcelUrl,
            ],
        ]);
    }

    public function statisticsUserPointExport($params, $task)
    {
        $userId = $task->user_id;
        $user   = User::query()->find($userId);
        request()->offsetSet('page_size', 1000000);
        $users   = ScopeQueryImpl::adminGetStatisticsUserPointQueryItems($user, $params);
        $headers = [
            '姓名',
            '部门',
            '积分',
        ];
        $data    = [];
        foreach ($users as $user) {
            $row    = [];
            $row[]  = $user->name;
            $row[]  = $user->district_name . $user->department_name;
            $row[]  = $user->points;
            $data[] = $row;
        }
        $path = storage_path('app/public/excel/');
        if (!file_exists($path)) {
            mkdir($path, 0777, true);
        }
        $exportExcelPath = $path . "积分列表-" . date('YmdHis') . '.xlsx';
        $this->saveExcel('积分列表', $headers, $data, $exportExcelPath);
        $exportExcelUrl = Str::replaceFirst(storage_path('app/public/'), '', $exportExcelPath);
        $task->update([
            'result' => [
                'url' => $exportExcelUrl,
            ],
        ]);
    }


    public function statisticsUserCallExport($params, $task)
    {
        $userId = $task->user_id;
        $user   = User::query()->find($userId);
        request()->offsetSet('page_size', 1000000);
        $users   = ScopeQueryImpl::adminGetStatisticsUserCallQueryItems($user, $params);
        $headers = [
            '姓名',
            '部门',
            '特殊时期征招拨打电话次数',
            '精准搜索匹配拨打电话次数',
        ];
        $data    = [];
        foreach ($users as $user) {
            $row    = [];
            $row[]  = $user->name;
            $row[]  = $user->district_name . $user->department_name;
            $row[]  = $user->recall_task_soldier_count;
            $row[]  = $user->teen_soldier_count;
            $data[] = $row;
        }
        $path = storage_path('app/public/excel/');
        if (!file_exists($path)) {
            mkdir($path, 0777, true);
        }
        $exportExcelPath = $path . "拨打电话次数列表-" . date('YmdHis') . '.xlsx';
        $this->saveExcel('拨打电话次数列表', $headers, $data, $exportExcelPath);
        $exportExcelUrl = Str::replaceFirst(storage_path('app/public/'), '', $exportExcelPath);
        $task->update([
            'result' => [
                'url' => $exportExcelUrl,
            ],
        ]);
    }

    public function statisticsUserCourseExport($params, $task)
    {
        $userId  = $task->user_id;
        $user    = User::query()->find($userId);
        $builder = ScopeQueryImpl::adminGetStatisticsUserCourseQuery($user, $params);
        $items   = $builder->get();
        $headers = [
            '姓名',
            '部门',
            '需学习',
            '已学习',
            '进度',
        ];
        $data    = [];
        foreach ($items as $item) {
            $row    = [];
            $row[]  = $item->name;
            $row[]  = $item->department_name;
            $row[]  = $item->need_study_count;
            $row[]  = $item->study_count;
            $row[]  = $item->need_study_count ? round($item->study_count / $item->need_study_count * 100, 2) . '%' : '0%';
            $data[] = $row;
        }
        $path = storage_path('app/public/excel/');
        if (!file_exists($path)) {
            mkdir($path, 0777, true);
        }
        $exportExcelPath = $path . "人员学习进度统计列表-" . date('YmdHis') . '.xlsx';
        $this->saveExcel('人员学习进度统计列表', $headers, $data, $exportExcelPath);
        $exportExcelUrl = Str::replaceFirst(storage_path('app/public/'), '', $exportExcelPath);
        $task->update([
            'result' => [
                'url' => $exportExcelUrl,
            ],
        ]);
    }

    public function statisticsDepartmentCourseExport($params, $task)
    {
        $userId = $task->user_id;
        $user   = User::query()->find($userId);
        request()->offsetSet('page_size', 1000000);
        $items   = ScopeQueryImpl::adminGetStatisticsDepartmentCourseQueryItems($user, $params);
        $headers = [
            '名称',
            '总人数',
            '已完成',
            '未完成',
            '进度',
        ];
        $data    = [];
        foreach ($items as $item) {
            $row    = [];
            $row[]  = $item->name;
            $row[]  = $item->total_count;
            $row[]  = $item->study_count;
            $row[]  = $item->unfinished_count;
            $row[]  = $item->total_count ? round($item->study_count / $item->total_count * 100, 2) . '%' : '0%';
            $data[] = $row;
        }
        $path = storage_path('app/public/excel/');
        if (!file_exists($path)) {
            mkdir($path, 0777, true);
        }
        $exportExcelPath = $path . "部门学习进度统计列表-" . date('YmdHis') . '.xlsx';
        $this->saveExcel('部门学习进度统计列表', $headers, $data, $exportExcelPath);
        $exportExcelUrl = Str::replaceFirst(storage_path('app/public/'), '', $exportExcelPath);
        $task->update([
            'result' => [
                'url' => $exportExcelUrl,
            ],
        ]);
    }

    public function statisticsCourseExport($params, $task)
    {
        $userId = $task->user_id;
        $user   = User::query()->find($userId);
        request()->offsetSet('page_size', 1000000);
        $builder = ScopeQueryImpl::adminGetStatisticsCourseQuery($user, $params);
        $items   = $builder->get();
        $headers = [
            '名称',
            '需学习',
            '已学习',
            '进度',
        ];
        $data    = [];
        foreach ($items as $item) {
            $row    = [];
            $row[]  = $item->course_name . $item->chapter_name;
            $row[]  = $item->need_study_count;
            $row[]  = $item->study_count;
            $row[]  = $item->need_study_count ? round($item->study_count / $item->need_study_count * 100, 2) . '%' : '0%';
            $data[] = $row;
        }
        $path = storage_path('app/public/excel/');
        if (!file_exists($path)) {
            mkdir($path, 0777, true);
        }
        $exportExcelPath = $path . "课程学习进度统计列表-" . date('YmdHis') . '.xlsx';
        $this->saveExcel('课程学习进度统计列表', $headers, $data, $exportExcelPath);
        $exportExcelUrl = Str::replaceFirst(storage_path('app/public/'), '', $exportExcelPath);
        $task->update([
            'result' => [
                'url' => $exportExcelUrl,
            ],
        ]);
    }

    public function statisticsUserPaperExport($params, $task)
    {
        $userId = $task->user_id;
        $user   = User::query()->find($userId);
        request()->offsetSet('page_size', 1000000);
        $items   = ScopeQueryImpl::adminGetStatisticsUserPaperQueryItems($user, $params);
        $headers = [
            '名称',
            '部门',
            '分数',
            '是否合格',
        ];
        $data    = [];
        foreach ($items as $item) {
            $row    = [];
            $row[]  = $item->name;
            $row[]  = $item->department_name;
            $row[]  = $item->max_score;
            $row[]  = $item->is_pass ? '合格' : '不合格';
            $data[] = $row;
        }
        $path = storage_path('app/public/excel/');
        if (!file_exists($path)) {
            mkdir($path, 0777, true);
        }
        $exportExcelPath = $path . "人员考试统计列表-" . date('YmdHis') . '.xlsx';
        $this->saveExcel('人员考试统计列表', $headers, $data, $exportExcelPath);
        $exportExcelUrl = Str::replaceFirst(storage_path('app/public/'), '', $exportExcelPath);
        $task->update([
            'result' => [
                'url' => $exportExcelUrl,
            ],
        ]);
    }

    public function statisticsDepartmentPaperExport($params, $task)
    {
        $userId = $task->user_id;
        $user   = User::query()->find($userId);
        request()->offsetSet('page_size', 1000000);
        $items   = ScopeQueryImpl::adminGetStatisticsDepartmentPaperQueryItems($user, $params);
        $headers = [
            '名称',
            '应参与人数',
            '参与人数',
            '未参与人数',
            '合格人数',
            '不合格人数',
            '进度',
        ];
        $data    = [];
        foreach ($items as $item) {
            $row    = [];
            $row[]  = $item->name;
            $row[]  = $item->total_count;
            $row[]  = $item->finished_count;
            $row[]  = $item->total_count - $item->finished_count;
            $row[]  = $item->passed_count;
            $row[]  = $item->total_count - $item->passed_count;
            $row[]  = $item->total_count ? round($item->passed_count / $item->total_count * 100, 2) : '0%';
            $data[] = $row;
        }
        $path = storage_path('app/public/excel/');
        if (!file_exists($path)) {
            mkdir($path, 0777, true);
        }
        $exportExcelPath = $path . "部门考试统计列表-" . date('YmdHis') . '.xlsx';
        $this->saveExcel('部门考试统计列表', $headers, $data, $exportExcelPath);
        $exportExcelUrl = Str::replaceFirst(storage_path('app/public/'), '', $exportExcelPath);
        $task->update([
            'result' => [
                'url' => $exportExcelUrl,
            ],
        ]);
    }

    public function workTaskReportExport($params, $task)
    {
        $work_task_id = $params['work_task_id'];
        $userId       = $task->user_id;
        $user         = User::query()->find($userId);
        request()->offsetSet('page_size', 1000000);
        $builder       = ScopeQueryImpl::adminGetWorkTaskReportQuery($user, $params);
        $items         = $builder->get();
        $workTask      = WorkTask::query()->find($work_task_id);
        $exportZipPath = storage_path('app/public/zip/' . $workTask->title . '-' . date('YmdHis'));
        if (!file_exists($exportZipPath)) {
            mkdir($exportZipPath, 0777, true);
        }
        foreach ($items as $item) {
            $reportSavePath = $exportZipPath;
            if ($item->district_name) {
                $reportSavePath .= '/' . $item->district_name;
            }
            if ($item->street_name) {
                $reportSavePath .= '/' . $item->street_name;
            }
            if ($item->department_name) {
                $reportSavePath .= '/' . $item->department_name;
            }
            if ($item->user_name) {
                $reportSavePath .= '/' . $item->user_name . '-' . Carbon::parse($item->created_at)->format('YmdHis');
            }
            if (!file_exists($reportSavePath)) {
                Log::info($reportSavePath);
                mkdir($reportSavePath, 0777, true);
            }
            $content     = $item->content;
            $attachments = Attachment::query()->whereIn('id', $item->file_ids)->get();
            $txtFileName = '内容' . '.txt';
            $txtFilePath = $reportSavePath . '/' . $txtFileName;
            $file        = fopen($txtFilePath, 'w');
            fwrite($file, $content);
            fclose($file);

            foreach ($attachments as $attachment) {
                try {
                    if ($attachment) {
                        // 读取网络文件写入文件夹
                        $filePath = $reportSavePath . '/' . '文件-' . $attachment->filename;
                        $file     = fopen($filePath, "w");
                        fwrite($file, file_get_contents($attachment->full_path));
                        fclose($file);
                    }
                } catch (\Exception $exception) {
                    Log::info($attachment->filename . "下载失败");
                    Log::info($exception->getMessage());
                }
            }
        }

        $zipPath = $exportZipPath . '/../' . Str::limit($workTask->title) . date('YmdHis') . '.zip';
        if (file_exists($zipPath)) {
            unlink($zipPath);
        }
        $zip = new ZipRepository();
        $zip->zip($zipPath, $exportZipPath);
        $task->update([
            'result' => [
                'url' => Str::replaceFirst(storage_path('app/public/'), '', $zipPath),
            ],
        ]);
    }

    public function surveyFormExport($params, $task)
    {
        $userId   = $task->user_id;
        $user     = User::query()->find($userId);
        $surveyId = $params['survey_id'] ?? 0;
        $builder  = ScopeQueryImpl::adminGetSurveyFormQuery($user, $params);
        $builder->orderBy("created_at", "desc");
        $items   = $builder->get();
        $survey  = Survey::query()->find($surveyId);
        $forms   = $survey->forms;
        $headers = [
            '填写时间',
            '填写人',
        ];
        foreach ($forms as $formItem) {
            $headers[] = $formItem['label'];
        }
        $data = [];
        foreach ($items as $item) {
            $row   = [];
            $row[] = $item->created_at;
            $row[] = $item->user_name;

            $itemForms = $item->forms;
            foreach ($itemForms as $formItem) {
                $value = $formItem['value'] ?? "";
                if (is_array($value)) {
                    $value = implode(",", $value);
                }
                $row[] = $value;
            }

            $data[] = $row;
        }
        $path = storage_path('app/public/excel/');
        if (!file_exists($path)) {
            mkdir($path, 0777, true);
        }
        $exportExcelPath = $path . "调查结果列表-" . date('YmdHis') . '.xlsx';
        $this->saveExcel('调查结果列表', $headers, $data, $exportExcelPath);
        $exportExcelUrl = Str::replaceFirst(storage_path('app/public/'), '', $exportExcelPath);
        $task->update([
            'result' => [
                'url' => $exportExcelUrl,
            ],
        ]);

    }
}
