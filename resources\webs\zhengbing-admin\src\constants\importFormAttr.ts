import { taskTypeMap } from '@/constants/index';

export const importListTypeMap = {
  [taskTypeMap.import_knowledge_excel.status]: {
    knowledge: {
      status: 'knowledge',
      text: '知识点列表',
    },
  },
  [taskTypeMap.import_question_excel.status]: {
    question: {
      status: 'question',
      text: '题目列表',
    },
  },
  [taskTypeMap.import_user_excel.status]: {
    user: {
      status: 'user',
      text: '用户列表',
    },
  },
  [taskTypeMap.import_out_notice_task_receive_excel.status]: {
    out_notice_task_receive: {
      status: 'out_notice_task_receive',
      text: '接收人列表',
    },
  },
  [taskTypeMap.import_recall_task_soldier_excel.status]: {
    recall_task_soldier: {
      status: 'recall_task_soldier',
      text: '特殊时期征召人员列表',
    },
  },
  [taskTypeMap.import_consult_knowledge_excel.status]: {
    consult_knowledge: {
      status: 'consult_knowledge',
      text: '咨询问答列表',
    },
  },
  [taskTypeMap.import_mission_init.status]: {
    mission_init_person: {
      status: 'mission_init_person',
      text: '人员列表',
    },
    mission_init_region_setting: {
      status: 'mission_init_region_setting',
      text: '任务数列表',
    },
  },
  [taskTypeMap.import_mission_promote_compare_person_sign_excel.status]: {
    mission_promote_compare_person_sign: {
      status: 'mission_promote_compare_person_sign',
      text: '报名名单',
    },
  },
  [taskTypeMap.import_mission_intranet_political_person_list.status]: {
    mission_political_person_list: {
      status: 'mission_political_person_list',
      text: '政考人员列表',
    },
  },
  [taskTypeMap.import_mission_intranet_education_person_list.status]: {
    mission_education_person_list: {
      status: 'mission_education_person_list',
      text: '役前教育人员列表',
    },
  },
  [taskTypeMap.import_mission_intranet_pre_store_person_list.status]: {
    mission_pre_store_person_list: {
      status: 'mission_pre_store_person_list',
      text: '预储人员列表',
    },
  },
  [taskTypeMap.import_mission_intranet_go_person_statistics_list.status]: {
    mission_go_person_statistics_list: {
      status: 'mission_go_person_statistics_list',
      text: '起运人员统计列表',
    },
  },
  [taskTypeMap.import_mission_physical_check_result.status]: {
    mission_physical_check_result: {
      status: 'mission_physical_check_result',
      text: '初次体检结果',
    },
  },
  [taskTypeMap.import_mission_physical_recheck_result.status]: {
    mission_physical_recheck_result: {
      status: 'mission_physical_recheck_result',
      text: '体检复查结果',
    },
  },
  [taskTypeMap.import_mission_physical_spot_check_result.status]: {
    mission_physical_spot_check_result: {
      status: 'mission_physical_spot_check_result',
      text: '体检抽查结果',
    },
  },
};

export const importListAttrMap = {
  knowledge: {
    name: {
      value: 'name',
      text: '知识点名称',
    },
  },
  question: {
    title: {
      value: 'title',
      text: '题目标题',
    },
    type: {
      value: 'type',
      text: '题目类型',
    },
    course_type_name: {
      value: 'course_type_name',
      text: '课程类型',
    },
    knowledge_name: {
      value: 'knowledge_name',
      text: '知识点',
    },
  },
  user: {
    name: {
      value: 'name',
      text: '用户名',
    },
    phone: {
      value: 'phone',
      text: '手机号',
    },
    email: {
      value: 'email',
      text: '邮箱',
    },
  },
  out_notice_task_receive: {
    name: {
      value: 'name',
      text: '接收人姓名',
    },
    phone: {
      value: 'phone',
      text: '手机号',
    },
    type: {
      value: 'type',
      text: '接收类型',
    },
  },
  recall_task_soldier: {
    name: {
      value: 'name',
      text: '姓名',
    },
    sex: {
      value: 'sex',
      text: '性别',
    },
    id_card: {
      value: 'id_card',
      text: '身份证号',
    },
    enlistment_time: {
      value: 'enlistment_time',
      text: '入伍时间',
    },
    enlistment_district_name: {
      value: 'enlistment_district_name',
      text: '入伍区县',
    },
    enlistment_street_name: {
      value: 'enlistment_street_name',
      text: '入伍街道',
    },
    education: {
      value: 'education',
      text: '文化程度',
    },
    political: {
      value: 'political',
      text: '政治面貌',
    },
    service_unit: {
      value: 'service_unit',
      text: '服现役大单位',
    },
    contact: {
      value: 'contact',
      text: '联系方式',
    },
    current_job: {
      value: 'current_job',
      text: '现工作单位',
    },
  },
  consult_knowledge: {
    title: {
      value: 'title',
      text: '问题',
    },
    content: {
      value: 'content',
      text: '答案',
    },
  },
  mission_init_person: {
    name: {
      value: 'name',
      text: '姓名',
    },
    phone: {
      value: 'phone',
      text: '联系方式',
      type: 'string',
    },
  },
  mission_init_region_setting: {
    name: {
      value: 'name',
      text: '名称',
    },
    task_num: {
      value: 'task_num',
      text: '任务数',
      type: 'string',
    },
    task_graduate_num: {
      value: 'task_graduate_num',
      text: ' 其中 : 毕业生任务数',
      type: 'string',
    },
    before_graduate_guide_ratio: {
      value: 'before_graduate_guide_ratio',
      text: '大学生征集指导比例',
      type: 'string',
    },
    graduate_guide_ratio: {
      value: 'graduate_guide_ratio',
      text: '大学毕业生征集指导比例',
      type: 'string',
    },
  },
  mission_promote_compare_person_sign: {
    name: {
      value: 'name',
      text: '名称',
    },
    id_card: {
      value: 'id_card',
      text: '身份证',
    },
  },

  // 后续阶段导入内网名单数据
  mission_political_person_list: {
    id_card: {
      value: 'id_card',
      text: '身份证号',
    },
    political_examination: {
      value: 'political_examination',
      text: '结果',
    },
  },
  mission_education_person_list: {
    id_card: {
      value: 'id_card',
      text: '身份证号',
    },
    education_result: {
      value: 'education_result',
      text: '结果',
    },
  },
  mission_pre_store_person_list: {
    id_card: {
      value: 'id_card',
      text: '身份证号',
    },
    pre_type: {
      value: 'pre_type',
      text: '预储类型',
    },
  },
  mission_go_person_statistics_list: {
    name: {
      value: 'name',
      text: '名称',
    },
  },
  mission_physical_check_result: {
    name: {
      value: 'name',
      text: '姓名',
    },
    id_card: {
      value: 'id_card',
      text: '身份证号',
    },
    result: {
      value: 'result',
      text: '结果',
    },
    remark: {
      value: 'remark',
      text: '不合格原因',
    },
  },
  mission_physical_recheck_result: {
    name: {
      value: 'name',
      text: '姓名',
    },
    id_card: {
      value: 'id_card',
      text: '身份证号',
    },
    result: {
      value: 'result',
      text: '结果',
    },
    remark: {
      value: 'remark',
      text: '不合格原因',
    },
  },
  mission_physical_spot_check_result: {
    name: {
      value: 'name',
      text: '姓名',
    },
    id_card: {
      value: 'id_card',
      text: '身份证号',
    },
    result: {
      value: 'result',
      text: '结果',
    },
    remark: {
      value: 'remark',
      text: '异常原因',
    },
  },
};

export const importDetailAttrMap = {
  knowledge: {
    name: {
      value: 'name',
      text: '知识点名称',
      type: 'string',
    },
  },
  question: {
    title: {
      value: 'title',
      text: '题目标题',
      type: 'string',
    },
    type: {
      value: 'type',
      text: '题目类型',
      type: 'string',
    },
    course_type_name: {
      value: 'course_type_name',
      text: '课程类型',
      type: 'string',
    },
    knowledge_name: {
      value: 'knowledge_name',
      text: '知识点',
      type: 'string',
    },
    content: {
      value: 'content',
      text: '题目内容',
      type: 'string',
    },
    answer: {
      value: 'answer',
      text: '答案',
      type: 'string',
    },
  },
  user: {
    name: {
      value: 'name',
      text: '用户名',
      type: 'string',
    },
    phone: {
      value: 'phone',
      text: '手机号',
      type: 'string',
    },
    email: {
      value: 'email',
      text: '邮箱',
      type: 'string',
    },
    role_name: {
      value: 'role_name',
      text: '角色',
      type: 'string',
    },
  },
  out_notice_task_receive: {
    name: {
      value: 'name',
      text: '接收人姓名',
      type: 'string',
    },
    phone: {
      value: 'phone',
      text: '手机号',
      type: 'string',
    },
    type: {
      value: 'type',
      text: '接收类型',
      type: 'string',
    },
  },
  mission_init_person: {
    name: {
      value: 'name',
      text: '姓名',
      type: 'string',
    },
    phone: {
      value: 'phone',
      text: '联系方式',
      type: 'string',
    },
    id_card: {
      value: 'id_card',
      text: '身份证号',
      type: 'string',
    },
    district_code: {
      value: 'district_code',
      text: '所在区域编码',
      type: 'string',
    },
    district_name: {
      value: 'district_name',
      text: '所在区域',
      type: 'string',
    },
    street_code: {
      value: 'street_code',
      text: '所在街道编码',
      type: 'string',
    },
    // 街道
    street_name: {
      value: 'street_name',
      text: '所在街道',
      type: 'string',
    },
    school_code: {
      value: 'school_code',
      text: '所在学校编码',
      type: 'string',
    },
    // 学校
    school_name: {
      value: 'school_name',
      text: '所在学校',
      type: 'string',
    },
  },
  mission_init_region_setting: {
    name: {
      value: 'name',
      text: '名称',
    },
    code: {
      value: 'code',
      text: '编码',
    },
    task_num: {
      value: 'task_num',
      text: '任务数',
      type: 'string',
    },
    task_graduate_num: {
      value: 'task_graduate_num',
      text: ' 其中 : 毕业生任务数',
      type: 'string',
    },
    before_graduate_guide_ratio: {
      value: 'before_graduate_guide_ratio',
      text: '大学生征集指导比例',
      type: 'string',
    },
    graduate_guide_ratio: {
      value: 'graduate_guide_ratio',
      text: '大学毕业生征集指导比例',
      type: 'string',
    },
  },
  mission_promote_compare_person_sign: {
    name: {
      value: 'name',
      text: '名称',
    },
    id_card: {
      value: 'id_card',
      text: '身份证',
    },
  },
  // 后续阶段导入内网名单数据
  mission_political_person_list: {
    id_card: {
      value: 'id_card',
      text: '身份证号',
    },
    political_examination: {
      value: 'political_examination',
      text: '结果',
    },
  },
  mission_education_person_list: {
    id_card: {
      value: 'id_card',
      text: '身份证号',
    },
    education_result: {
      value: 'education_result',
      text: '结果',
    },
  },
  mission_pre_store_person_list: {
    id_card: {
      value: 'id_card',
      text: '身份证号',
    },
    pre_type: {
      value: 'pre_type',
      text: '预储类型',
    },
  },
  mission_go_person_statistics_list: {
    name: {
      value: 'name',
      text: '名称',
    },
    code: {
      value: 'code',
      text: '编码',
    },
  },
  mission_physical_check_result: {
    name: {
      value: 'name',
      text: '姓名',
    },
    id_card: {
      value: 'id_card',
      text: '身份证号',
    },
    result: {
      value: 'result',
      text: '结果',
    },
    remark: {
      value: 'remark',
      text: '不合格原因',
    },
  },
  mission_physical_recheck_result: {
    name: {
      value: 'name',
      text: '姓名',
    },
    id_card: {
      value: 'id_card',
      text: '身份证号',
    },
    result: {
      value: 'result',
      text: '结果',
    },
    remark: {
      value: 'remark',
      text: '不合格原因',
    },
  },
  mission_physical_spot_check_result: {
    name: {
      value: 'name',
      text: '姓名',
    },
    id_card: {
      value: 'id_card',
      text: '身份证号',
    },
    result: {
      value: 'result',
      text: '结果',
    },
    remark: {
      value: 'remark',
      text: '异常原因',
    },
  },
};
