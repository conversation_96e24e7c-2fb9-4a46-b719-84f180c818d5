<?php

namespace App\Http\Services\Admin\Task\Import;

use App\Http\Consts\CategoryConst;
use App\Http\Consts\ImportConst;
use App\Http\Consts\QuestionConst;
use App\Exceptions\ErrorTrait;
use App\Models\Category;
use App\Models\CourseType;
use App\Models\ImportItem;
use App\Models\Question;


use App\Models\Task;
use Illuminate\Support\Facades\DB;

class QuestionImportService
{
    use ErrorTrait;

    private $task;

    public function handle(Task $task)
    {
        $this->task = $task;

        $INSERT_BATCH_SIZE = env('INSERT_BATCH_SIZE', 100);

        $queryBuilder = ImportItem::query()
            ->where('task_id', $task->id)
            ->whereIn('checked', [ImportConst::importItemCheckCheckAccepted])
            ->whereIn('imported', [
                ImportConst::importItemImportedToDo,
                ImportConst::importItemImportedError,
            ]);

        $queryBuilder->clone()
            ->where('type', ImportConst::importItemTypeQuestion)
            ->chunkById($INSERT_BATCH_SIZE, function ($items) {
                $this->importQuestion($items);
            });
    }

    private function importQuestion($items)
    {
        foreach ($items as $item) {
            DB::beginTransaction();
            try {
                $data = $item->data;
                
                $title = trim($data['title'] ?? '');
                $type = trim($data['type'] ?? '');
                $courseTypeName = trim($data['course_type_name'] ?? '');
                $knowledgeName = trim($data['knowledge_name'] ?? '');
                $analysis = trim($data['analysis'] ?? '');
                $rightAnswer = trim($data['right_answer'] ?? '');
                $answerOptions = $data['answer_options'] ?? [];

                // 基本验证
                if (empty($title)) {
                    throw new \Exception('题目标题不能为空');
                }

                if (empty($type)) {
                    throw new \Exception('题目类型不能为空');
                }

                // 验证题目类型
                $typeMap = array_flip(QuestionConst::typeMap);
                if (!isset($typeMap[$type])) {
                    throw new \Exception('无效的题目类型：' . $type);
                }
                $typeValue = $typeMap[$type];

                // 获取或创建课程类型
                $courseTypeId = null;
                if (!empty($courseTypeName)) {
                    $courseType = CourseType::query()->firstOrCreate([
                        'name' => $courseTypeName,
                        'template' => CategoryConst::templateCourse,
                    ], [
                        'name' => $courseTypeName,
                        'template' => CategoryConst::templateCourse,
                    ]);
                    $courseTypeId = $courseType->id;
                }

                // 创建题目
                $question = Question::query()->create([
                    'course_type_id' => $courseTypeId,
                    'type' => $typeValue,
                    'title' => $title,
                    'analysis' => $analysis,
                ]);

                // 创建答案选项
                foreach ($answerOptions as $answerIndex => $answerText) {
                    if (!empty(trim($answerText))) {
                        $question->answers()->create([
                            'title' => trim($answerText),
                            'is_correct' => str_contains($rightAnswer, chr(ord('A') + $answerIndex)),
                        ]);
                    }
                }

                // 关联知识点
                if (!empty($knowledgeName)) {
                    $knowledgeNames = explode(',', $knowledgeName);
                    $knowledgeIdArr = [];
                    foreach ($knowledgeNames as $name) {
                        $name = trim($name);
                        if (!empty($name)) {
                            $knowledge = Category::query()->firstOrCreate([
                                'name' => $name,
                                'template' => CategoryConst::templateKnowledge,
                            ], [
                                'name' => $name,
                                'template' => CategoryConst::templateKnowledge,
                            ]);
                            $knowledgeIdArr[] = $knowledge->id;
                        }
                    }

                    // 使用sync方法批量关联知识点
                    if (!empty($knowledgeIdArr)) {
                        $question->knowledges()->sync($knowledgeIdArr);
                    }
                }

                $item->update([
                    'imported' => ImportConst::importItemImportedDone,
                    'message' => '导入成功'
                ]);

                DB::commit();
            } catch (\Exception $e) {
                DB::rollBack();
                $item->update([
                    'imported' => ImportConst::importItemImportedError,
                    'message' => '导入失败：' . $e->getMessage()
                ]);
            }
        }
    }
}
