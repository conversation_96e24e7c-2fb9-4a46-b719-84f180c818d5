<?php

namespace App\Http\Services\Admin\Task\Check;

use App\Http\Consts\ImportConst;
use App\Exceptions\ErrorTrait;
use App\Models\ImportItem;
use App\Models\Role;
use App\Models\User;

class UserCheckService
{
    use ErrorTrait;

    private $task;

    public function handle($task)
    {
        $this->task = $task;

        $INSERT_BATCH_SIZE = env('INSERT_BATCH_SIZE', 100);

        $queryBuilder = ImportItem::query()
            ->where('task_id', $task->id)
            ->whereIn('checked', [
                ImportConst::importItemCheckUncheck,
                ImportConst::importItemCheckError,
            ]);

        $queryBuilder->clone()
            ->where('type', ImportConst::importItemTypeUser)
            ->chunkById($INSERT_BATCH_SIZE, function ($items) {
                foreach ($items as $item) {
                    $this->checkUser($item);
                }
            });
    }

    public function checkUser(ImportItem $item)
    {
        try {
            $data = $item->data;
            $errors = [];

            // 检查用户名
            $name = trim($data['name'] ?? '');
            if (empty($name)) {
                $errors[] = '用户名不能为空';
            } else if (mb_strlen($name) > 50) {
                $errors[] = '用户名不能超过50个字符';
            }

            // 检查手机号
            $phone = trim($data['phone'] ?? '');
            if (empty($phone)) {
                $errors[] = '手机号不能为空';
            } else {
                // 验证手机号格式
                if (!preg_match('/^1[3-9]\d{9}$/', $phone)) {
                    $errors[] = '手机号格式不正确';
                } else {
                    // 检查手机号是否已存在
                    $existingUser = User::query()->where('phone', $phone)->first();
                    if ($existingUser) {
                        $errors[] = '手机号已存在：' . $phone;
                    }
                }
            }

            // 检查邮箱（可选）
            $email = trim($data['email'] ?? '');
            if (!empty($email)) {
                if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                    $errors[] = '邮箱格式不正确';
                } else if (mb_strlen($email) > 100) {
                    $errors[] = '邮箱不能超过100个字符';
                } else {
                    // 检查邮箱是否已存在
                    $existingEmailUser = User::query()->where('email', $email)->first();
                    if ($existingEmailUser) {
                        $errors[] = '邮箱已存在：' . $email;
                    }
                }
            }

            // 检查角色名称（可选）
            $roleName = trim($data['role_name'] ?? '');
            if (!empty($roleName)) {
                $role = Role::query()->where('name', $roleName)->first();
                if (!$role) {
                    $errors[] = '角色不存在：' . $roleName;
                }
            }

            // 更新检查状态
            if (empty($errors)) {
                $item->update([
                    'checked' => ImportConst::importItemCheckCheckAccepted,
                    'message' => '校验通过',
                    'checked_at' => now(),
                ]);
            } else {
                $item->update([
                    'checked' => ImportConst::importItemCheckError,
                    'message' => implode('; ', $errors),
                    'checked_at' => now(),
                ]);
            }

            return empty($errors);
        } catch (\Exception $e) {
            $item->update([
                'checked' => ImportConst::importItemCheckError,
                'message' => '校验异常：' . $e->getMessage(),
                'checked_at' => now(),
            ]);
            return false;
        }
    }
}
