<?php

namespace App\Http\Services\Admin\Task;

use App\Exceptions\ErrorTrait;
use App\Http\Consts\ImportConst;
use App\Http\Consts\TaskConst;
use App\Http\Services\Admin\Task\Check\MissionInitCheckService;
use App\Http\Services\Admin\Task\Check\MissionManyPersonListCheckService;
use App\Http\Services\Admin\Task\Check\MissionPromoteComparePersonSignCheckService;
use App\Http\Services\Admin\Task\Check\KnowledgeCheckService;
use App\Http\Services\Admin\Task\Check\QuestionCheckService;
use App\Http\Services\Admin\Task\Check\UserCheckService;
use App\Http\Services\Admin\Task\Check\OutNoticeTaskReceiveCheckService;
use App\Http\Services\Admin\Task\Check\RecallTaskSoldierCheckService;
use App\Http\Services\Admin\Task\Check\ConsultKnowledgeCheckService;
use App\Jobs\TaskJob;
use App\Models\ImportItem;
use App\Models\Task;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class ImportItemService
{
    use ErrorTrait;

    public function index($params)
    {
        $builder = ImportItem::query();

        if (isset($params['type']) && $params['type']) {
            $builder->where('type', $params['type']);
        }

        if (isset($params['task_id']) && $params['task_id']) {
            $builder->where('task_id', $params['task_id']);
        }

        // imported
        if (isset($params['imported']) && $params['imported']) {
            $builder->where('imported', $params['imported']);
        }

        // checked
        if (isset($params['checked']) && $params['checked']) {
            $builder->where('checked', $params['checked']);
        }

        $items = $builder->orderBy('id', 'desc')->paginate(pageSize());

        return $items;
    }

    public function show($id)
    {
        $item = ImportItem::query()->find($id);
        return $item;
    }

    public function update($id, array $params)
    {
        $va = Validator::make($params, [
            'data' => 'required',
        ], [
            'data.required' => '数据不能为空',
        ]);
        if ($va->fails()) {
            $this->setError($va->errors()->first());
            return false;
        }

        $data = [
            'data' => $params['data'] ?? null
        ];

        DB::beginTransaction();
        try {
            $item = ImportItem::query()->find($id);
            if ($item) {
                $item->update($data);
                switch ($item->type) {
                    case ImportConst::importItemTypeMissionInitPerson:
                        $service = new MissionInitCheckService();
                        $service->checkPerson($item);
                        break;
                    case ImportConst::importItemTypeMissionInitRegionSetting:
                        $service = new MissionInitCheckService();
                        $service->checkRegionSetting($item);
                        break;

                    case ImportConst::importItemTypeMissionPoliticalPersonList:
                        $service = new MissionManyPersonListCheckService();
                        $service->checkPoliticalPersonList($item);
                    case ImportConst::importItemTypeMissionEducationPersonList:
                        $service = new MissionManyPersonListCheckService();
                        $service->checkEducationPersonList($item);
                        break;
                    case ImportConst::importItemTypeMissionPreStorePersonList:
                        $service = new MissionManyPersonListCheckService();
                        $service->checkPreStorePersonList($item);
                        break;
                    case ImportConst::importItemTypeMissionGoPersonStatisticsList:
                        $service = new MissionManyPersonListCheckService();
                        $service->checkGoPersonList($item);
                        break;

                    case ImportConst::importItemTypeMissionPromoteComparePersonSign:
                        $service = new MissionPromoteComparePersonSignCheckService();
                        $service->checkPersonSign($item);
                        break;

                    case ImportConst::importItemTypeKnowledge:
                        $service = new KnowledgeCheckService();
                        $service->checkKnowledge($item);
                        break;

                    case ImportConst::importItemTypeQuestion:
                        $service = new QuestionCheckService();
                        $service->checkQuestion($item);
                        break;

                    case ImportConst::importItemTypeUser:
                        $service = new UserCheckService();
                        $service->checkUser($item);
                        break;

                    case ImportConst::importItemTypeOutNoticeTaskReceive:
                        // 获取任务ID用于校验
                        $task = Task::query()->find($item->task_id);
                        $taskId = $task->params['task_id'] ?? null;
                        $service = new OutNoticeTaskReceiveCheckService($taskId);
                        $service->checkOutNoticeTaskReceive($item);
                        break;

                    case ImportConst::importItemTypeRecallTaskSoldier:
                        $service = new RecallTaskSoldierCheckService();
                        $service->checkRecallTaskSoldier($item);
                        break;

                    case ImportConst::importItemTypeConsultKnowledge:
                        $service = new ConsultKnowledgeCheckService();
                        $service->checkConsultKnowledge($item);
                        break;
                }
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $this->setError($e->getMessage());
            return false;
        }
        return $data;
    }
}
