<?php

namespace App\Http\Services\Mini;

use App\Exceptions\ErrorTrait;
use App\Http\Utils\ScopeQueryImpl;
use App\Models\MissionPhysicalExamination;
use App\Models\MissionPerson;
use App\Models\RegionManager;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class MissionPhysicalExaminationService
{
    use ErrorTrait;

    /**
     * 获取体检计划列表
     * 
     * @param array $params
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function list(array $params)
    {
        $user = auth()->user();
        $builder = ScopeQueryImpl::adminGetMissionPhysicalExaminationQuery($user, $params);
        $builder->orderBy("id", "desc");
        $items = $builder->paginate(pageSize());
        return $items;
    }

    /**
     * 获取体检人员列表
     * 
     * @param array $params
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function personList(array $params)
    {
        $user = auth()->user();
        $builder = ScopeQueryImpl::adminGetMissionPersonQuery($user, $params);
        $builder->orderBy("id", "desc");
        $items = $builder->paginate(pageSize());
        return $items;
    }
} 