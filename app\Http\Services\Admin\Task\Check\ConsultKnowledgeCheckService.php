<?php

namespace App\Http\Services\Admin\Task\Check;

use App\Http\Consts\ImportConst;
use App\Exceptions\ErrorTrait;
use App\Models\ImportItem;
use App\Models\Article;
use App\Http\Consts\ArticleConst;

class ConsultKnowledgeCheckService
{
    use ErrorTrait;

    protected $task;

    public function check($task)
    {
        $this->task = $task;

        $importItems = ImportItem::query()
            ->where('task_id', $task->id)
            ->where('checked', ImportConst::importItemCheckUncheck)
            ->get();

        foreach ($importItems as $item) {
            $errors = $this->validateItem($item->data);

            if (empty($errors)) {
                $item->update([
                    'checked' => ImportConst::importItemCheckCheckAccepted,
                    'message' => '校验通过',
                    'checked_at' => now(),
                ]);
            } else {
                $item->update([
                    'checked' => ImportConst::importItemCheckError,
                    'message' => implode('; ', $errors),
                    'checked_at' => now(),
                ]);
            }
        }
    }

    protected function validateItem($data)
    {
        $errors = [];

        // 检查问题标题
        $title = trim($data['title'] ?? '');
        if (empty($title)) {
            $errors[] = '问题标题不能为空';
        } elseif (mb_strlen($title) > 255) {
            $errors[] = '问题标题长度不能超过255个字符';
        } else {
            // 检查是否重复
            $exists = Article::query()
                ->where('title', $title)
                ->where('template', ArticleConst::templateConsultKnowledge)
                ->exists();
            if ($exists) {
                $errors[] = '问题标题已存在：' . $title;
            }
        }

        // 检查答案内容
        $content = trim($data['content'] ?? '');
        if (empty($content)) {
            $errors[] = '答案内容不能为空';
        } elseif (mb_strlen($content) > 10000) {
            $errors[] = '答案内容长度不能超过10000个字符';
        }

        return $errors;
    }

    public function checkConsultKnowledge($item)
    {
        $errors = $this->validateItem($item->data);

        if (empty($errors)) {
            $item->update([
                'checked' => ImportConst::importItemCheckCheckAccepted,
                'message' => '校验通过',
                'checked_at' => now(),
            ]);
        } else {
            $item->update([
                'checked' => ImportConst::importItemCheckError,
                'message' => implode('; ', $errors),
                'checked_at' => now(),
            ]);
        }
    }
}
