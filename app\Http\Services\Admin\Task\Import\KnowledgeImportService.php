<?php

namespace App\Http\Services\Admin\Task\Import;

use App\Http\Consts\CategoryConst;
use App\Http\Consts\ImportConst;
use App\Exceptions\ErrorTrait;
use App\Models\Category;
use App\Models\ImportItem;
use App\Models\Task;
use Illuminate\Support\Facades\DB;

class KnowledgeImportService
{
    use ErrorTrait;

    private $task;

    public function handle(Task $task)
    {
        $this->task = $task;

        $INSERT_BATCH_SIZE = env('INSERT_BATCH_SIZE', 100);

        $queryBuilder = ImportItem::query()
            ->where('task_id', $task->id)
            ->whereIn('checked', [ImportConst::importItemCheckCheckAccepted])
            ->whereIn('imported', [
                ImportConst::importItemImportedToDo,
                ImportConst::importItemImportedError,
            ]);

        $queryBuilder->clone()
            ->where('type', ImportConst::importItemTypeKnowledge)
            ->chunkById($INSERT_BATCH_SIZE, function ($items) {
                $this->importKnowledge($items);
            });
    }

    private function importKnowledge($items)
    {
        foreach ($items as $item) {
            DB::beginTransaction();
            try {
                $data = $item->data;
                $name = trim($data['name'] ?? '');

                if (empty($name)) {
                    throw new \Exception('知识点名称不能为空');
                }

                // 检查是否已存在
                $existingKnowledge = Category::query()
                    ->where('name', $name)
                    ->where('template', CategoryConst::templateKnowledge)
                    ->first();

                if ($existingKnowledge) {
                    // 如果已存在，标记为导入成功但不重复创建
                    $item->update([
                        'imported' => ImportConst::importItemImportedDone,
                        'message' => '知识点已存在，跳过创建'
                    ]);
                } else {
                    // 创建新知识点
                    Category::query()->create([
                        'name' => $name,
                        'template' => CategoryConst::templateKnowledge,
                    ]);

                    $item->update([
                        'imported' => ImportConst::importItemImportedDone,
                        'message' => '导入成功'
                    ]);
                }

                DB::commit();
            } catch (\Exception $e) {
                DB::rollBack();
                $item->update([
                    'imported' => ImportConst::importItemImportedError,
                    'message' => '导入失败：' . $e->getMessage()
                ]);
            }
        }
    }
}
