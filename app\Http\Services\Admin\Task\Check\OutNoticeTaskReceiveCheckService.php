<?php

namespace App\Http\Services\Admin\Task\Check;

use App\Http\Consts\ImportConst;
use App\Http\Consts\OutNoticeConst;
use App\Exceptions\ErrorTrait;
use App\Models\ImportItem;
use App\Models\OutNoticeTaskReceive;

class OutNoticeTaskReceiveCheckService
{
    use ErrorTrait;

    private $task;
    private $taskId;

    public function handle($task)
    {
        $this->task = $task;
        $this->taskId = $task->params['task_id'] ?? null;

        $INSERT_BATCH_SIZE = env('INSERT_BATCH_SIZE', 100);

        $queryBuilder = ImportItem::query()
            ->where('task_id', $task->id)
            ->whereIn('checked', [
                ImportConst::importItemCheckUncheck,
                ImportConst::importItemCheckError,
            ]);

        $queryBuilder->clone()
            ->where('type', ImportConst::importItemTypeOutNoticeTaskReceive)
            ->chunkById($INSERT_BATCH_SIZE, function ($items) {
                foreach ($items as $item) {
                    $this->checkOutNoticeTaskReceive($item);
                }
            });
    }

    public function checkOutNoticeTaskReceive(ImportItem $item)
    {
        try {
            $data = $item->data;
            $errors = [];

            // 检查接收人姓名
            $name = trim($data['name'] ?? '');
            if (empty($name)) {
                $errors[] = '接收人姓名不能为空';
            } else if (mb_strlen($name) > 50) {
                $errors[] = '接收人姓名不能超过50个字符';
            }

            // 检查手机号
            $phone = trim($data['phone'] ?? '');
            if (empty($phone)) {
                $errors[] = '手机号不能为空';
            } else {
                // 验证手机号格式
                if (!preg_match('/^1[3-9]\d{9}$/', $phone)) {
                    $errors[] = '手机号格式不正确';
                } else if ($this->taskId) {
                    // 检查在同一任务中是否已存在相同手机号
                    $existingReceive = OutNoticeTaskReceive::query()
                        ->where('task_id', $this->taskId)
                        ->where('phone', $phone)
                        ->first();
                    
                    if ($existingReceive) {
                        $errors[] = '该手机号已存在于接收人列表中：' . $phone;
                    }
                }
            }

            // 检查接收类型（可选）
            $type = trim($data['type'] ?? '');
            if (!empty($type)) {
                $validTypes = [
                    OutNoticeConst::taskReceiveTypeSms,
                    OutNoticeConst::taskReceiveTypeVms,
                    OutNoticeConst::taskReceiveTypeRobot,
                ];

                if (!in_array($type, $validTypes)) {
                    $errors[] = '无效的接收类型：' . $type . '，有效类型：' . implode(', ', $validTypes);
                }
            }

            // 更新检查状态
            if (empty($errors)) {
                $item->update([
                    'checked' => ImportConst::importItemCheckCheckAccepted,
                    'message' => '校验通过',
                    'checked_at' => now(),
                ]);
            } else {
                $item->update([
                    'checked' => ImportConst::importItemCheckError,
                    'message' => implode('; ', $errors),
                    'checked_at' => now(),
                ]);
            }

            return empty($errors);
        } catch (\Exception $e) {
            $item->update([
                'checked' => ImportConst::importItemCheckError,
                'message' => '校验异常：' . $e->getMessage(),
                'checked_at' => now(),
            ]);
            return false;
        }
    }
}
