<?php

namespace App\Http\Services\Admin\Task\Check;

use App\Http\Consts\CategoryConst;
use App\Http\Consts\ImportConst;
use App\Exceptions\ErrorTrait;
use App\Models\Category;
use App\Models\ImportItem;

class KnowledgeCheckService
{
    use ErrorTrait;

    private $task;

    public function handle($task)
    {
        $this->task = $task;

        $INSERT_BATCH_SIZE = env('INSERT_BATCH_SIZE', 100);

        $queryBuilder = ImportItem::query()
            ->where('task_id', $task->id)
            ->whereIn('checked', [
                ImportConst::importItemCheckUncheck,
                ImportConst::importItemCheckError,
            ]);

        $queryBuilder->clone()
            ->where('type', ImportConst::importItemTypeKnowledge)
            ->chunkById($INSERT_BATCH_SIZE, function ($items) {
                foreach ($items as $item) {
                    $this->checkKnowledge($item);
                }
            });
    }

    public function checkKnowledge(ImportItem $item)
    {
        try {
            $data = $item->data;
            $errors = [];

            // 检查知识点名称
            $name = trim($data['name'] ?? '');
            if (empty($name)) {
                $errors[] = '知识点名称不能为空';
            } else {
                // 检查名称长度
                if (mb_strlen($name) > 100) {
                    $errors[] = '知识点名称不能超过100个字符';
                }

                // 检查是否已存在
                $existingKnowledge = Category::query()
                    ->where('name', $name)
                    ->where('template', CategoryConst::templateKnowledge)
                    ->first();

                if ($existingKnowledge) {
                    $errors[] = '知识点已存在：' . $name;
                }
            }

            // 更新检查状态
            if (empty($errors)) {
                $item->update([
                    'checked' => ImportConst::importItemCheckCheckAccepted,
                    'message' => '校验通过',
                    'checked_at' => now(),
                ]);
            } else {
                $item->update([
                    'checked' => ImportConst::importItemCheckError,
                    'message' => implode('; ', $errors),
                    'checked_at' => now(),
                ]);
            }

            return empty($errors);
        } catch (\Exception $e) {
            $item->update([
                'checked' => ImportConst::importItemCheckError,
                'message' => '校验异常：' . $e->getMessage(),
                'checked_at' => now(),
            ]);
            return false;
        }
    }
}
