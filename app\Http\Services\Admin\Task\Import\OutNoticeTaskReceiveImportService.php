<?php

namespace App\Http\Services\Admin\Task\Import;

use App\Http\Consts\ImportConst;
use App\Http\Consts\OutNoticeConst;
use App\Exceptions\ErrorTrait;
use App\Models\ImportItem;
use App\Models\OutNoticeTaskReceive;
use App\Models\Task;
use Illuminate\Support\Facades\DB;

class OutNoticeTaskReceiveImportService
{
    use ErrorTrait;

    private $task;

    public function handle(Task $task)
    {
        $this->task = $task;

        $INSERT_BATCH_SIZE = env('INSERT_BATCH_SIZE', 100);

        $queryBuilder = ImportItem::query()
            ->where('task_id', $task->id)
            ->whereIn('checked', [ImportConst::importItemCheckCheckAccepted])
            ->whereIn('imported', [
                ImportConst::importItemImportedToDo,
                ImportConst::importItemImportedError,
            ]);

        $queryBuilder->clone()
            ->where('type', ImportConst::importItemTypeOutNoticeTaskReceive)
            ->chunkById($INSERT_BATCH_SIZE, function ($items) {
                $this->importOutNoticeTaskReceive($items);
            });
    }

    private function importOutNoticeTaskReceive($items)
    {
        foreach ($items as $item) {
            DB::beginTransaction();
            try {
                $data = $item->data;
                
                $name = trim($data['name'] ?? '');
                $phone = trim($data['phone'] ?? '');
                $type = trim($data['type'] ?? '');
                $rowParams = $data['row_params'] ?? [];

                // 基本验证
                if (empty($name)) {
                    throw new \Exception('接收人姓名不能为空');
                }

                if (empty($phone)) {
                    throw new \Exception('手机号不能为空');
                }

                // 验证手机号格式
                if (!preg_match('/^1[3-9]\d{9}$/', $phone)) {
                    throw new \Exception('手机号格式不正确');
                }

                // 验证接收类型
                $validTypes = [
                    OutNoticeConst::taskReceiveTypeSms,
                    OutNoticeConst::taskReceiveTypeVms,
                    OutNoticeConst::taskReceiveTypeRobot,
                ];

                if (!empty($type) && !in_array($type, $validTypes)) {
                    throw new \Exception('无效的接收类型：' . $type);
                }

                // 默认类型为短信
                if (empty($type)) {
                    $type = OutNoticeConst::taskReceiveTypeSms;
                }

                // 获取任务ID（从任务参数中获取）
                $taskId = $this->task->params['task_id'] ?? null;
                if (!$taskId) {
                    throw new \Exception('缺少外呼任务ID');
                }

                // 获取外呼任务信息
                $outNoticeTask = \App\Models\OutNoticeTask::query()->find($taskId);
                if (!$outNoticeTask) {
                    throw new \Exception('外呼任务不存在');
                }

                // 根据任务类型创建不同的接收记录
                $taskType = $outNoticeTask->type;

                if ($taskType == OutNoticeConst::taskTypeSmsVms) {
                    // SMS+VMS任务：创建两条记录
                    if (!OutNoticeTaskReceive::query()->where([
                        'task_id' => $taskId,
                        'phone' => $phone,
                        'type' => OutNoticeConst::taskReceiveTypeSms,
                    ])->exists()) {
                        OutNoticeTaskReceive::query()->create([
                            'params' => $rowParams, // 保存完整的行参数
                            'task_id' => $taskId,
                            'type' => OutNoticeConst::taskReceiveTypeSms,
                            'name' => $name,
                            'phone' => $phone,
                            'status' => OutNoticeConst::taskReceiveStatusCreated,
                        ]);
                    }

                    if (!OutNoticeTaskReceive::query()->where([
                        'task_id' => $taskId,
                        'phone' => $phone,
                        'type' => OutNoticeConst::taskReceiveTypeVms,
                    ])->exists()) {
                        OutNoticeTaskReceive::query()->create([
                            'params' => $rowParams, // 保存完整的行参数
                            'task_id' => $taskId,
                            'type' => OutNoticeConst::taskReceiveTypeVms,
                            'name' => $name,
                            'phone' => $phone,
                            'status' => OutNoticeConst::taskReceiveStatusCreated,
                        ]);
                    }
                } else {
                    // 其他任务类型：创建单条记录，使用任务类型作为接收类型
                    if (!OutNoticeTaskReceive::query()->where([
                        'task_id' => $taskId,
                        'phone' => $phone,
                        'type' => $taskType, // 使用任务类型，不是用户输入的type
                    ])->exists()) {
                        OutNoticeTaskReceive::query()->create([
                            'params' => $rowParams, // 保存完整的行参数
                            'task_id' => $taskId,
                            'name' => $name,
                            'phone' => $phone,
                            'type' => $taskType, // 使用任务类型
                            'status' => OutNoticeConst::taskReceiveStatusCreated,
                        ]);
                    }
                }

                $item->update([
                    'imported' => ImportConst::importItemImportedDone,
                    'message' => '导入成功'
                ]);

                DB::commit();
            } catch (\Exception $e) {
                DB::rollBack();
                $item->update([
                    'imported' => ImportConst::importItemImportedError,
                    'message' => '导入失败：' . $e->getMessage()
                ]);
            }
        }
    }
}
