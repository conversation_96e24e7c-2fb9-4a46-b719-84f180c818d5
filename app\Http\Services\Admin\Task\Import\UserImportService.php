<?php

namespace App\Http\Services\Admin\Task\Import;

use App\Http\Consts\ImportConst;
use App\Exceptions\ErrorTrait;
use App\Models\ImportItem;
use App\Models\Role;
use App\Models\Task;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class UserImportService
{
    use ErrorTrait;

    private $task;

    public function handle(Task $task)
    {
        $this->task = $task;

        $INSERT_BATCH_SIZE = env('INSERT_BATCH_SIZE', 100);

        $queryBuilder = ImportItem::query()
            ->where('task_id', $task->id)
            ->whereIn('checked', [ImportConst::importItemCheckCheckAccepted])
            ->whereIn('imported', [
                ImportConst::importItemImportedToDo,
                ImportConst::importItemImportedError,
            ]);

        $queryBuilder->clone()
            ->where('type', ImportConst::importItemTypeUser)
            ->chunkById($INSERT_BATCH_SIZE, function ($items) {
                $this->importUser($items);
            });
    }

    private function importUser($items)
    {
        foreach ($items as $item) {
            DB::beginTransaction();
            try {
                $data = $item->data;
                
                $name = trim($data['name'] ?? '');
                $phone = trim($data['phone'] ?? '');
                $email = trim($data['email'] ?? '');
                $roleName = trim($data['role_name'] ?? '');

                // 基本验证
                if (empty($name)) {
                    throw new \Exception('用户名不能为空');
                }

                if (empty($phone)) {
                    throw new \Exception('手机号不能为空');
                }

                // 验证手机号格式
                if (!preg_match('/^1[3-9]\d{9}$/', $phone)) {
                    throw new \Exception('手机号格式不正确');
                }

                // 验证邮箱格式（如果提供）
                if (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
                    throw new \Exception('邮箱格式不正确');
                }

                // 检查用户是否已存在
                $existingUser = User::query()
                    ->where('phone', $phone)
                    ->first();

                if ($existingUser) {
                    throw new \Exception('手机号已存在：' . $phone);
                }

                // 如果提供了邮箱，检查邮箱是否已存在
                if (!empty($email)) {
                    $existingEmailUser = User::query()
                        ->where('email', $email)
                        ->first();

                    if ($existingEmailUser) {
                        throw new \Exception('邮箱已存在：' . $email);
                    }
                }

                // 获取角色ID
                $roleId = null;
                if (!empty($roleName)) {
                    $role = Role::query()->where('name', $roleName)->first();
                    if (!$role) {
                        throw new \Exception('角色不存在：' . $roleName);
                    }
                    $roleId = $role->id;
                }

                // 创建用户
                $user = User::query()->create([
                    'name' => $name,
                    'phone' => $phone,
                    'email' => $email,
                    'password' => Hash::make('123456'), // 默认密码，建议后续修改
                    'role_id' => $roleId,
                    'status' => 1, // 默认启用状态
                ]);

                $item->update([
                    'imported' => ImportConst::importItemImportedDone,
                    'message' => '导入成功，默认密码：123456'
                ]);

                DB::commit();
            } catch (\Exception $e) {
                DB::rollBack();
                $item->update([
                    'imported' => ImportConst::importItemImportedError,
                    'message' => '导入失败：' . $e->getMessage()
                ]);
            }
        }
    }
}
