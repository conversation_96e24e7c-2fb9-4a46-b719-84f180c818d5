<template>
  <div class="list-common-table">
    <t-form ref="form" :data="formData" label-align="left" :label-width="60" @reset="onReset" @submit="onSearch">
      <t-row :gutter="30">
        <t-col :span="4">
          <t-form-item label="问题" name="name">
            <t-input v-model="formData.name" class="form-item-content" type="search" placeholder="请输入问题" />
          </t-form-item>
        </t-col>

        <t-col :span="4" class="operation-container">
          <t-button theme="primary" type="submit"> 查询</t-button>
          <!--导出-->
          <export-button
            :form-data="formData"
            :task-type="taskTypeMap.export_consult_knowledge_excel.status"
          ></export-button>
          <t-button type="reset" variant="base" theme="default"> 重置</t-button>
        </t-col>
      </t-row>
    </t-form>

    <t-space style="margin-top: 16px">
      <t-popconfirm
        v-if="selectedRowKeys.length > 0"
        content="确认删除吗"
        @confirm="handleClickDelete(selectedRowKeys)"
      >
        <t-button theme="danger">
          <template #icon>
            <t-icon name="delete" />
          </template>
          批量删除
        </t-button>
      </t-popconfirm>
      <t-button @click="handleClickCreate">
        <template #icon>
          <t-icon name="plus" />
        </template>
        新建
      </t-button>
      <!--导入-->
      <import-button
        :task-type="taskTypeMap.import_consult_knowledge_excel.status"
        :template-url="'咨询问答导入模板.xlsx'"
        @close="fetchData"
      />
    </t-space>

    <div class="table-container">
      <t-table
        :select-on-row-click="true"
        :data="list"
        :columns="COLUMNS"
        :row-key="rowKey"
        :vertical-align="verticalAlign"
        :hover="hover"
        :pagination="pagination"
        :loading="dataLoading"
        :header-affixed-top="headerAffixedTop"
        :selected-row-keys="selectedRowKeys"
        @page-change="rehandlePageChange"
        @change="rehandleChange"
        @select-change="rehandleSelectChange"
      >
        <template #op="slotProps">
          <t-space @click.stop>
            <t-link theme="primary" @click="handleClickEdit(slotProps)"> 修改</t-link>
            <t-popconfirm content="确认删除吗" @confirm="handleClickDelete([slotProps.row.id])">
              <t-link theme="danger"> 删除</t-link>
            </t-popconfirm>
          </t-space>
        </template>
      </t-table>
    </div>
  </div>
</template>
<script setup lang="tsx">
import { PageInfo, PrimaryTableCol, TableProps, TableRowData } from 'tdesign-vue-next';
import { computed, onMounted, ref } from 'vue';

import { getCategoryList } from '@/api/category';
import { deleteConsultKnowledge, getConsultKnowledgeList, updateConsultKnowledge } from '@/api/consultKnowledge';
import ImportButton from '@/components/task/excel/importButton.vue';
import ExportButton from '@/components/task/exportButton.vue';
import { prefix } from '@/config/global';
import { articleTemplateMap, categoryTemplateMap, taskTypeMap } from '@/constants';
import router from '@/router';
import { useSettingStore } from '@/store';

interface FormData {
  name: string;
  category_id: string;
  template: string;
}

const store = useSettingStore();

const COLUMNS: PrimaryTableCol[] = [
  {
    colKey: 'row-select',
    type: 'multiple',
  },
  {
    title: '问题',
    ellipsis: true,
    colKey: 'title',
  },
  // 禁用
  {
    title: '禁用',
    colKey: 'disabled',
    cell: (h, { row }) => {
      return (
        <t-switch
          size="small"
          value={row.disabled === 0}
          onChange={(value: any, context: { e: MouseEvent }) => {
            context.e.stopPropagation();
            handleClickDisabled(row);
          }}
        />
      );
    },
  },
  {
    title: '创建时间',
    colKey: 'created_at',
  },
  {
    align: 'left',
    fixed: 'right',
    width: 160,
    colKey: 'op',
    title: '操作',
  },
];

const searchFormInit = {
  name: '',
  category_id: '',
  template: articleTemplateMap.consult_knowledge.status,
};

const formData = ref<FormData>({ ...searchFormInit });
const rowKey = 'id';
const verticalAlign = 'top' as const;
const hover = true;

const pagination = ref({
  pageSize: 20,
  total: 100,
  current: 1,
});

const list = ref([]);

const dataLoading = ref(false);

onMounted(() => {
  fetchData();
  getCategories();
});

const fetchData = async () => {
  dataLoading.value = true;
  try {
    const query = {
      ...formData.value,
      page: pagination.value.current,
      page_size: pagination.value.pageSize,
    };
    const { data, total, current_page, per_page } = await getConsultKnowledgeList(query);
    list.value = data;
    pagination.value = {
      total,
      current: current_page,
      pageSize: parseInt(String(per_page), 10),
    };
  } catch (e) {
    console.log(e);
  } finally {
    dataLoading.value = false;
  }
};

const handleClickEdit = (slotProps: { row: TableRowData }) => {
  console.log(slotProps.row);
  router.push(`/basic/consult-knowledge-edit/${slotProps.row.id}`);
};

const handleClickCreate = () => {
  router.push('/basic/consult-knowledge-create');
};

// 多选
const selectedRowKeys = ref<TableProps['selectedRowKeys']>([]);
const rehandleSelectChange: TableProps['onSelectChange'] = (value, ctx) => {
  selectedRowKeys.value = value;
  console.log(value, ctx);
};

// 删除
const handleClickDelete = (ids: Array<String | Number>) => {
  deleteConsultKnowledge({
    id: 'batch',
    ids,
  }).then((res) => {
    console.log(res);
    fetchData();
  });
};

// 置顶
const handleClickTop = (row: TableRowData) => {
  row.top = !row.top;
  updateConsultKnowledge(row.id, { top: row.top, v: 'switch' }).then((res) => {
    console.log(res);
    fetchData();
  });
};

// 禁用
const handleClickDisabled = (row: TableRowData) => {
  row.disabled = !row.disabled;
  updateConsultKnowledge(row.id, { disabled: row.disabled, v: 'switch' }).then((res) => {
    console.log(res);
    fetchData();
  });
};

// 搜索
const onReset = (val: unknown) => {
  console.log(val);
  formData.value = { ...searchFormInit };
};
const onSearch = (val: unknown) => {
  console.log(val);
  console.log(formData.value);
  fetchData();
};
const rehandlePageChange = (pageInfo: PageInfo, newDataSource: TableRowData[]) => {
  console.log('分页变化', pageInfo, newDataSource);
  pagination.value.pageSize = pageInfo.pageSize;
  pagination.value.current = pageInfo.current;
  fetchData();
};
const rehandleChange = (changeParams: unknown, triggerAndData: unknown) => {
  console.log('统一Change', changeParams, triggerAndData);
};

const headerAffixedTop = computed(
  () =>
    ({
      offsetTop: store.isUseTabsRouter ? 48 : 0,
      container: `.${prefix}-layout`,
    }) as any, // TO BE FIXED
);

const categories = ref([]);

const getCategories = () => {
  getCategoryList({
    page_size: 999,
    template: categoryTemplateMap.article.status,
  }).then((res) => {
    categories.value = res.data;
  });
};
</script>

<style lang="less" scoped>
.list-common-table {
  background-color: var(--td-bg-color-container);
  padding: var(--td-comp-paddingTB-xxl) var(--td-comp-paddingLR-xxl);
  border-radius: var(--td-radius-medium);

  .table-container {
    margin-top: var(--td-comp-margin-xxl);
  }
}

.form-item-content {
  width: 100%;
}

.operation-container {
  display: flex;
  justify-content: flex-start;
  align-items: center;

  .expand {
    .t-button__text {
      display: flex;
      align-items: center;
    }
  }
}

.payment-col {
  display: flex;

  .trend-container {
    display: flex;
    align-items: center;
    margin-left: var(--td-comp-margin-s);
  }
}
</style>
