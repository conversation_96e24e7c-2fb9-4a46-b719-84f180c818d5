<?php

namespace App\Http\Services\Admin\Task\Save;

use App\Http\Consts\ImportConst;
use App\Exceptions\ErrorTrait;
use App\Http\Traits\ExcelTraits;
use App\Models\ImportItem;

class RecallTaskSoldierSavingService
{
    use ErrorTrait, ExcelTraits;

    protected $task;

    public function import($params, $task)
    {
        $this->task = $task;
        $attachmentId = $params['attachment_id'] ?? '';

        $rows = $this->readExcelFormAttachmentId($attachmentId);

        $dataItems = [];

        foreach ($rows as $index => $row) {
            if ($index == 1) {
                continue; // 跳过表头行
            }
            
            $name = trim($row[1] ?? '');
            $sex = trim($row[2] ?? '');
            $idCard = trim($row[3] ?? '');
            $enlistmentTime = trim($row[4] ?? '');
            $enlistmentDistrictName = trim($row[5] ?? '');
            $enlistmentStreetName = trim($row[6] ?? '');
            $education = trim($row[7] ?? '');
            $majorType = trim($row[8] ?? '');
            $political = trim($row[9] ?? '');
            $serviceUnit = trim($row[10] ?? '');
            $serviceUnitSecondary = trim($row[11] ?? '');
            $serviceJob = trim($row[12] ?? '');
            $majorTypeSecondary = trim($row[13] ?? '');
            $backboneStatus = trim($row[14] ?? '');
            $dischargeTime = trim($row[15] ?? '');
            $dischargeRank = trim($row[16] ?? '');
            $enlistmentProvince = trim($row[17] ?? '');
            $homeAddress = trim($row[18] ?? '');
            $currentJob = trim($row[19] ?? '');
            $currentMajor = trim($row[20] ?? '');
            $contact = trim($row[21] ?? '');
            $familyContact = trim($row[22] ?? '');
            $isMarried = trim($row[23] ?? '');
            $isOnlyChild = trim($row[24] ?? '');
            $hasBirth = trim($row[25] ?? '');
            
            // 跳过空行
            if (empty($name) && empty($idCard)) {
                continue;
            }

            $dataItems[] = [
                'name' => $name,
                'sex' => $sex,
                'id_card' => $idCard,
                'enlistment_time' => $enlistmentTime,
                'enlistment_district_name' => $enlistmentDistrictName,
                'enlistment_street_name' => $enlistmentStreetName,
                'education' => $education,
                'major_type' => $majorType,
                'political' => $political,
                'service_unit' => $serviceUnit,
                'service_unit_secondary' => $serviceUnitSecondary,
                'service_job' => $serviceJob,
                'major_type_secondary' => $majorTypeSecondary,
                'backbone_status' => $backboneStatus,
                'discharge_time' => $dischargeTime,
                'discharge_rank' => $dischargeRank,
                'enlistment_province' => $enlistmentProvince,
                'home_address' => $homeAddress,
                'current_job' => $currentJob,
                'current_major' => $currentMajor,
                'contact' => $contact,
                'family_contact' => $familyContact,
                'is_married' => $isMarried,
                'is_only_child' => $isOnlyChild,
                'has_birth' => $hasBirth,
            ];
        }

        $this->handleItems($dataItems);
    }

    protected function handleItems($dataItems)
    {
        foreach ($dataItems as $data) {
            ImportItem::query()->create([
                'task_id' => $this->task->id,
                'type' => ImportConst::importItemTypeRecallTaskSoldier,
                'data' => $data,
                'checked' => ImportConst::importItemCheckUncheck,
                'imported' => ImportConst::importItemImportedToDo,
            ]);
        }
    }
}
