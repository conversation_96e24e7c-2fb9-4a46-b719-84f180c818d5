<?php

namespace App\Http\Services\Admin\Task\Check;

use App\Http\Consts\ImportConst;
use App\Exceptions\ErrorTrait;
use App\Models\ImportItem;
use App\Models\RecallTaskSoldier;
use Carbon\Carbon;

class RecallTaskSoldierCheckService
{
    use ErrorTrait;

    protected $task;

    public function handle($task)
    {
        $this->task = $task;

        $INSERT_BATCH_SIZE = env('INSERT_BATCH_SIZE', 100);

        $queryBuilder = ImportItem::query()
            ->where('task_id', $task->id)
            ->whereIn('checked', [
                ImportConst::importItemCheckUncheck,
                ImportConst::importItemCheckError,
            ]);

        $queryBuilder->clone()
            ->where('type', ImportConst::importItemTypeRecallTaskSoldier)
            ->chunkById($INSERT_BATCH_SIZE, function ($items) {
                foreach ($items as $item) {
                    $this->checkRecallTaskSoldier($item);
                }
            });
    }

    public function checkRecallTaskSoldier($item)
    {
        $errors = $this->validateItem($item->data);

        if (empty($errors)) {
            $item->update([
                'checked' => ImportConst::importItemCheckCheckAccepted,
                'message' => '校验通过',
                'checked_at' => now(),
            ]);
        } else {
            $item->update([
                'checked' => ImportConst::importItemCheckError,
                'message' => implode('; ', $errors),
                'checked_at' => now(),
            ]);
        }
    }

    protected function validateItem($data)
    {
        $errors = [];

        // 检查姓名
        $name = trim($data['name'] ?? '');
        if (empty($name)) {
            $errors[] = '姓名不能为空';
        } elseif (mb_strlen($name) > 50) {
            $errors[] = '姓名长度不能超过50个字符';
        }

        // 检查性别
        $sex = trim($data['sex'] ?? '');
        if (!empty($sex)) {
            $validSex = ['男', '女', '1', '2'];
            if (!in_array($sex, $validSex)) {
                $errors[] = '性别格式错误，应为：男、女、1、2';
            }
        }

        // 检查身份证号
        $idCard = trim($data['id_card'] ?? '');
        if (!empty($idCard)) {
            if (!preg_match('/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/', $idCard)) {
                $errors[] = '身份证号格式错误';
            } else {
                // 检查是否重复
                $recallTaskId = $this->task->params['task_id'] ?? null;
                if ($recallTaskId) {
                    $exists = RecallTaskSoldier::query()
                        ->where('task_id', $recallTaskId)
                        ->where('id_card', $idCard)
                        ->exists();
                    if ($exists) {
                        $errors[] = '身份证号已存在：' . $idCard;
                    }
                }
            }
        }

        // 检查入伍时间
        $enlistmentTime = trim($data['enlistment_time'] ?? '');
        if (!empty($enlistmentTime)) {
            try {
                Carbon::parse($enlistmentTime);
            } catch (\Exception $e) {
                $errors[] = '入伍时间格式错误';
            }
        }

        // 检查退伍时间
        $dischargeTime = trim($data['discharge_time'] ?? '');
        if (!empty($dischargeTime)) {
            try {
                Carbon::parse($dischargeTime);
            } catch (\Exception $e) {
                $errors[] = '退伍时间格式错误';
            }
        }

        // 检查联系方式
        $contact = trim($data['contact'] ?? '');
        if (!empty($contact)) {
            if (!preg_match('/^1[3-9]\d{9}$/', $contact)) {
                $errors[] = '联系方式格式错误';
            }
        }

        // 检查家庭联系方式
        $familyContact = trim($data['family_contact'] ?? '');
        if (!empty($familyContact)) {
            if (!preg_match('/^1[3-9]\d{9}$/', $familyContact)) {
                $errors[] = '家庭联系方式格式错误';
            }
        }

        // 检查是否结婚
        $isMarried = trim($data['is_married'] ?? '');
        if (!empty($isMarried)) {
            $validMarried = ['是', '否', '1', '0'];
            if (!in_array($isMarried, $validMarried)) {
                $errors[] = '是否结婚格式错误，应为：是、否、1、0';
            }
        }

        // 检查是否独生子女
        $isOnlyChild = trim($data['is_only_child'] ?? '');
        if (!empty($isOnlyChild)) {
            $validOnlyChild = ['是', '否', '1', '0'];
            if (!in_array($isOnlyChild, $validOnlyChild)) {
                $errors[] = '是否独生子女格式错误，应为：是、否、1、0';
            }
        }

        // 检查是否生育
        $hasBirth = trim($data['has_birth'] ?? '');
        if (!empty($hasBirth)) {
            $validBirth = ['是', '否', '1', '0'];
            if (!in_array($hasBirth, $validBirth)) {
                $errors[] = '是否生育格式错误，应为：是、否、1、0';
            }
        }

        return $errors;
    }

    public function checkRecallTaskSoldier($item)
    {
        // 设置task用于校验
        $this->task = \App\Models\Task::query()->find($item->task_id);

        $errors = $this->validateItem($item->data);

        if (empty($errors)) {
            $item->update([
                'checked' => ImportConst::importItemCheckCheckAccepted,
                'message' => '校验通过',
                'checked_at' => now(),
            ]);
        } else {
            $item->update([
                'checked' => ImportConst::importItemCheckError,
                'message' => implode('; ', $errors),
                'checked_at' => now(),
            ]);
        }
    }
}
