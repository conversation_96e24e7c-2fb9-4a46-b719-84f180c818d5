<?php

namespace App\Http\Utils;

use App\Http\Consts\PermissionSlugConst;
use App\Models\Permission;

class PermissionUtil
{
    public static function getPermission()
    {
        return [
            [
                "name"  => "管理端",
                "items" => [
                    [
                        "name"  => "账号",
                        "items" => [
                            [
                                'name'  => '个人账号',
                                'items' => [
                                    ["name" => "后台登录", "slug" => PermissionSlugConst::admin_login, "uri" => "/api/admin/login", "method" => "POST"],
                                    ["name" => "账号信息", "slug" => PermissionSlugConst::admin_info, "uri" => "/api/admin/info", "method" => "GET"],
                                    ["name" => "修改密码", "slug" => PermissionSlugConst::admin_update_password, "uri" => "/api/admin/update_password", "method" => "POST"],
                                ]
                            ]
                        ]
                    ],
                    [
                        "name"  => '系统配置',
                        "items" => [
                            [
                                'name'  => '配置',
                                'items' => [
                                    ["name" => "系统配置-查看", "slug" => PermissionSlugConst::admin_config_get, "uri" => "/api/admin/config", "method" => "GET"],
                                    ["name" => "系统配置-修改", "slug" => PermissionSlugConst::admin_config_post, "uri" => "/api/admin/config", "method" => "POST"],
                                ]
                            ],
                            // 角色管理
                            [
                                'name'  => '角色管理',
                                'items' => [
                                    ["name" => "角色管理-查看", "slug" => PermissionSlugConst::admin_role_show, "uri" => "/api/admin/role/*", "method" => "GET"],
                                    ["name" => "角色管理-列表", "slug" => PermissionSlugConst::admin_role_list, "uri" => "/api/admin/role", "method" => "GET"],
                                    ["name" => "角色管理-新增", "slug" => PermissionSlugConst::admin_role_store, "uri" => "/api/admin/role", "method" => "POST"],
                                    ["name" => "角色管理-修改", "slug" => PermissionSlugConst::admin_role_update, "uri" => "/api/admin/role/*", "method" => "PUT"],
                                    ["name" => "角色管理-删除", "slug" => PermissionSlugConst::admin_role_delete, "uri" => "/api/admin/role/*", "method" => "DELETE"],
                                ]
                            ],
                            // 权限管理
                            [
                                'name'  => '权限管理',
                                'items' => [
                                    ["name" => "权限管理-查看", "slug" => PermissionSlugConst::admin_permission_show, "uri" => "/api/admin/permission/*", "method" => "GET"],
                                    ["name" => "权限管理-列表", "slug" => PermissionSlugConst::admin_permission_list, "uri" => "/api/admin/permission", "method" => "GET"],
                                    ["name" => "权限管理-新增", "slug" => PermissionSlugConst::admin_permission_store, "uri" => "/api/admin/permission", "method" => "POST"],
                                    ["name" => "权限管理-修改", "slug" => PermissionSlugConst::admin_permission_update, "uri" => "/api/admin/permission/*", "method" => "PUT"],
                                    ["name" => "权限管理-删除", "slug" => PermissionSlugConst::admin_permission_delete, "uri" => "/api/admin/permission/*", "method" => "DELETE"],
                                    ["name" => "权限管理-树状列表", "slug" => PermissionSlugConst::admin_permission_tree, "uri" => "/api/admin/permission-tree", "method" => "GET"],
                                ]
                            ]
                        ]
                    ],
                    [
                        "name"  => '用户管理',
                        "items" => [
                            // 组织架构
                            [
                                'name'  => '组织架构',
                                'items' => [
                                    ["name" => "组织架构-查看", "slug" => PermissionSlugConst::admin_region_show, "uri" => "/api/admin/region/*", "method" => "GET"],
                                    ["name" => "组织架构-树状列表", "slug" => PermissionSlugConst::admin_region_tree, "uri" => "/api/admin/region_tree", "method" => "GET"],
                                    ["name" => "组织架构-列表", "slug" => PermissionSlugConst::admin_region_list, "uri" => "/api/admin/region", "method" => "GET"],
                                    ["name" => "组织架构-新增", "slug" => PermissionSlugConst::admin_region_store, "uri" => "/api/admin/region", "method" => "POST"],
                                    ["name" => "组织架构-修改", "slug" => PermissionSlugConst::admin_region_update, "uri" => "/api/admin/region/*", "method" => "PUT"],
                                    ["name" => "组织架构-删除", "slug" => PermissionSlugConst::admin_region_delete, "uri" => "/api/admin/region/*", "method" => "DELETE"],
                                ],
                            ],
                            // 用户管理
                            [
                                'name'  => '用户管理',
                                'items' => [
                                    ["name" => "用户管理-查看", "slug" => PermissionSlugConst::admin_user_show, "uri" => "/api/admin/user/*", "method" => "GET"],
                                    ["name" => "用户管理-列表", "slug" => PermissionSlugConst::admin_user_list, "uri" => "/api/admin/user", "method" => "GET"],
                                    ["name" => "用户管理-新增", "slug" => PermissionSlugConst::admin_user_store, "uri" => "/api/admin/user", "method" => "POST"],
                                    ["name" => "用户管理-修改", "slug" => PermissionSlugConst::admin_user_update, "uri" => "/api/admin/user/*", "method" => "PUT"],
                                    ["name" => "用户管理-删除", "slug" => PermissionSlugConst::admin_user_delete, "uri" => "/api/admin/user/*", "method" => "DELETE"],
                                    ["name" => "用户管理-重置密码", "slug" => PermissionSlugConst::admin_user_password_reset, "uri" => "/api/admin/user/*/password_reset", "method" => "POST"]
                                ],
                            ],
                            // 业务类型管理
                            [
                                'name'  => '业务类型管理',
                                'items' => [
                                    ["name" => "业务类型管理-查看", "slug" => PermissionSlugConst::admin_business_category_show, "uri" => "/api/admin/business-category/*", "method" => "GET"],
                                    ["name" => "业务类型管理-列表", "slug" => PermissionSlugConst::admin_business_category_list, "uri" => "/api/admin/business-category", "method" => "GET"],
                                    ["name" => "业务类型管理-新增", "slug" => PermissionSlugConst::admin_business_category_store, "uri" => "/api/admin/business-category", "method" => "POST"],
                                    ["name" => "业务类型管理-修改", "slug" => PermissionSlugConst::admin_business_category_update, "uri" => "/api/admin/business-category/*", "method" => "PUT"],
                                    ["name" => "业务类型管理-删除", "slug" => PermissionSlugConst::admin_business_category_delete, "uri" => "/api/admin/business-category/*", "method" => "DELETE"],
                                ],
                            ],
                            // 注册码
                            [
                                'name'  => '注册码',
                                'items' => [
                                    ["name" => "注册码-查看", "slug" => PermissionSlugConst::admin_invite_code_show, "uri" => "/api/admin/invitation_code/*", "method" => "GET"],
                                    ["name" => "注册码-列表", "slug" => PermissionSlugConst::admin_invite_code_list, "uri" => "/api/admin/invitation_code", "method" => "GET"],
                                    ["name" => "注册码-新增", "slug" => PermissionSlugConst::admin_invite_code_store, "uri" => "/api/admin/invitation_code", "method" => "POST"],
                                    ["name" => "注册码-修改", "slug" => PermissionSlugConst::admin_invite_code_update, "uri" => "/api/admin/invitation_code/*", "method" => "PUT"],
                                    ["name" => "注册码-删除", "slug" => PermissionSlugConst::admin_invite_code_delete, "uri" => "/api/admin/invitation_code/*", "method" => "DELETE"],
                                    ["name" => "注册码-清空", "slug" => PermissionSlugConst::admin_invite_code_remove_all, "uri" => "/api/admin/invitation_code_remove_all", "method" => "POST"],
                                ],
                            ],
                        ]
                    ],
                    [
                        "name"  => '基础数据管理',
                        "items" => [
                            [
                                'name'  => '分类管理',
                                "items" => [
                                    ["name" => "分类管理-查看", "slug" => PermissionSlugConst::admin_category_show, "uri" => "/api/admin/category/*", "method" => "GET"],
                                    ["name" => "分类管理-列表", "slug" => PermissionSlugConst::admin_category_list, "uri" => "/api/admin/category", "method" => "GET"],
                                    ["name" => "分类管理-新增", "slug" => PermissionSlugConst::admin_category_store, "uri" => "/api/admin/category", "method" => "POST"],
                                    ["name" => "分类管理-修改", "slug" => PermissionSlugConst::admin_category_update, "uri" => "/api/admin/category/*", "method" => "PUT"],
                                    ["name" => "分类管理-删除", "slug" => PermissionSlugConst::admin_category_delete, "uri" => "/api/admin/category/*", "method" => "DELETE"],
                                ],
                            ],
                            [
                                'name'  => '文章管理',
                                "items" => [
                                    ["name" => "文章管理-查看", "slug" => PermissionSlugConst::admin_article_show, "uri" => "/api/admin/article/*", "method" => "GET"],
                                    ["name" => "文章管理-列表", "slug" => PermissionSlugConst::admin_article_list, "uri" => "/api/admin/article", "method" => "GET"],
                                    ["name" => "文章管理-新增", "slug" => PermissionSlugConst::admin_article_store, "uri" => "/api/admin/article", "method" => "POST"],
                                    ["name" => "文章管理-修改", "slug" => PermissionSlugConst::admin_article_update, "uri" => "/api/admin/article/*", "method" => "PUT"],
                                    ["name" => "文章管理-删除", "slug" => PermissionSlugConst::admin_article_delete, "uri" => "/api/admin/article/*", "method" => "DELETE"],
                                ],
                            ],
                            [
                                'name'  => '菜单管理',
                                "items" => [
                                    ["name" => "菜单管理-查看", "slug" => PermissionSlugConst::admin_menu_show, "uri" => "/api/admin/menu/*", "method" => "GET"],
                                    ["name" => "菜单管理-列表", "slug" => PermissionSlugConst::admin_menu_list, "uri" => "/api/admin/menu", "method" => "GET"],
                                    ["name" => "菜单管理-新增", "slug" => PermissionSlugConst::admin_menu_store, "uri" => "/api/admin/menu", "method" => "POST"],
                                    ["name" => "菜单管理-修改", "slug" => PermissionSlugConst::admin_menu_update, "uri" => "/api/admin/menu/*", "method" => "PUT"],
                                    ["name" => "菜单管理-删除", "slug" => PermissionSlugConst::admin_menu_delete, "uri" => "/api/admin/menu/*", "method" => "DELETE"],
                                ],
                            ],
                            [
                                'name'  => '页面管理',
                                "items" => [
                                    ["name" => "页面管理-查看", "slug" => PermissionSlugConst::admin_page_show, "uri" => "/api/admin/page/*", "method" => "GET"],
                                    ["name" => "页面管理-列表", "slug" => PermissionSlugConst::admin_page_list, "uri" => "/api/admin/page", "method" => "GET"],
                                    ["name" => "页面管理-新增", "slug" => PermissionSlugConst::admin_page_store, "uri" => "/api/admin/page", "method" => "POST"],
                                    ["name" => "页面管理-修改", "slug" => PermissionSlugConst::admin_page_update, "uri" => "/api/admin/page/*", "method" => "PUT"],
                                    ["name" => "页面管理-删除", "slug" => PermissionSlugConst::admin_page_delete, "uri" => "/api/admin/page/*", "method" => "DELETE"],
                                ],
                            ],
                            [
                                'name'  => '咨询问答管理',
                                "items" => [
                                    ["name" => "咨询问答管理-查看", "slug" => PermissionSlugConst::admin_consult_knowledge_show, "uri" => "/api/admin/consult_knowledge/*", "method" => "GET"],
                                    ["name" => "咨询问答管理-列表", "slug" => PermissionSlugConst::admin_consult_knowledge_list, "uri" => "/api/admin/consult_knowledge", "method" => "GET"],
                                    ["name" => "咨询问答管理-新增", "slug" => PermissionSlugConst::admin_consult_knowledge_store, "uri" => "/api/admin/consult_knowledge", "method" => "POST"],
                                    ["name" => "咨询问答管理-修改", "slug" => PermissionSlugConst::admin_consult_knowledge_update, "uri" => "/api/admin/consult_knowledge/*", "method" => "PUT"],
                                    ["name" => "咨询问答管理-删除", "slug" => PermissionSlugConst::admin_consult_knowledge_delete, "uri" => "/api/admin/consult_knowledge/*", "method" => "DELETE"],
                                ],
                            ],
                            [
                                'name'  => '咨询资料',
                                "items" => [
                                    ["name" => "咨询资料-查看", "slug" => PermissionSlugConst::admin_consult_file_show, "uri" => "/api/admin/consult_file/*", "method" => "GET"],
                                    ["name" => "咨询资料-列表", "slug" => PermissionSlugConst::admin_consult_file_list, "uri" => "/api/admin/consult_file", "method" => "GET"],
                                    ["name" => "咨询资料-新增", "slug" => PermissionSlugConst::admin_consult_file_store, "uri" => "/api/admin/consult_file", "method" => "POST"],
                                    ["name" => "咨询资料-修改", "slug" => PermissionSlugConst::admin_consult_file_update, "uri" => "/api/admin/consult_file/*", "method" => "PUT"],
                                    ["name" => "咨询资料-删除", "slug" => PermissionSlugConst::admin_consult_file_delete, "uri" => "/api/admin/consult_file/*", "method" => "DELETE"],
                                ],
                            ],
                        ]
                    ],
                    [
                        "name"  => '课程管理',
                        "items" => [

                            // 学年管理
                            [
                                'name'  => '学年管理',
                                'items' => [
                                    ["name" => "学年列表", "slug" => PermissionSlugConst::admin_stage_list, "uri" => "/api/admin/stage", "method" => "GET"],
                                    ["name" => "学年详情", "slug" => PermissionSlugConst::admin_stage_show, "uri" => "/api/admin/stage/*", "method" => "GET"],
                                    ["name" => "学年新增", "slug" => PermissionSlugConst::admin_stage_store, "uri" => "/api/admin/stage", "method" => "POST"],
                                    ["name" => "学年修改", "slug" => PermissionSlugConst::admin_stage_update, "uri" => "/api/admin/stage/*", "method" => "PUT"],
                                    ["name" => "学年删除", "slug" => PermissionSlugConst::admin_stage_delete, "uri" => "/api/admin/stage/*", "method" => "DELETE"],
                                ],
                            ],
                            //课程类型
                            [
                                'name'  => '课程类型管理',
                                'items' => [
                                    ["name" => "课程类型管理-查看", "slug" => PermissionSlugConst::admin_course_type_show, "uri" => "/api/admin/course_type/*", "method" => "GET"],
                                    ["name" => "课程类型管理-列表", "slug" => PermissionSlugConst::admin_course_type_list, "uri" => "/api/admin/course_type", "method" => "GET"],
                                    ["name" => "课程类型管理-新增", "slug" => PermissionSlugConst::admin_course_type_store, "uri" => "/api/admin/course_type", "method" => "POST"],
                                    ["name" => "课程类型管理-修改", "slug" => PermissionSlugConst::admin_course_type_update, "uri" => "/api/admin/course_type/*", "method" => "PUT"],
                                    ["name" => "课程类型管理-删除", "slug" => PermissionSlugConst::admin_course_type_delete, "uri" => "/api/admin/course_type/*", "method" => "DELETE"],
                                ],
                            ],
                            //课程管理
                            [
                                'name'  => '课程管理',
                                'items' => [
                                    ["name" => "课程管理-查看", "slug" => PermissionSlugConst::admin_course_show, "uri" => "/api/admin/course/*", "method" => "GET"],
                                    ["name" => "课程管理-列表", "slug" => PermissionSlugConst::admin_course_list, "uri" => "/api/admin/course", "method" => "GET"],
                                    ["name" => "课程管理-新增", "slug" => PermissionSlugConst::admin_course_store, "uri" => "/api/admin/course", "method" => "POST"],
                                    ["name" => "课程管理-修改", "slug" => PermissionSlugConst::admin_course_update, "uri" => "/api/admin/course/*", "method" => "PUT"],
                                    ["name" => "课程管理-删除", "slug" => PermissionSlugConst::admin_course_delete, "uri" => "/api/admin/course/*", "method" => "DELETE"],
                                ],
                            ],
                            [
                                'name'  => '课程内容管理',
                                'items' => [
                                    ["name" => "课程内容管理-查看", "slug" => PermissionSlugConst::admin_chapter_show, "uri" => "/api/admin/chapter/*", "method" => "GET"],
                                    ["name" => "课程内容管理-列表", "slug" => PermissionSlugConst::admin_chapter_list, "uri" => "/api/admin/chapter", "method" => "GET"],
                                    ["name" => "课程内容管理-新增", "slug" => PermissionSlugConst::admin_chapter_store, "uri" => "/api/admin/chapter", "method" => "POST"],
                                    ["name" => "课程内容管理-修改", "slug" => PermissionSlugConst::admin_chapter_update, "uri" => "/api/admin/chapter/*", "method" => "PUT"],
                                    ["name" => "课程内容管理-删除", "slug" => PermissionSlugConst::admin_chapter_delete, "uri" => "/api/admin/chapter/*", "method" => "DELETE"],
                                ],
                            ],
                        ]
                    ],
                    [
                        "name"  => '练习考核',
                        "items" => [
                            // 题库管理
                            [
                                'name'  => '题库管理',
                                'items' => [
                                    ["name" => "题库管理-查看", "slug" => PermissionSlugConst::admin_question_show, "uri" => "/api/admin/question/*", "method" => "GET"],
                                    ["name" => "题库管理-列表", "slug" => PermissionSlugConst::admin_question_list, "uri" => "/api/admin/question", "method" => "GET"],
                                    ["name" => "题库管理-新增", "slug" => PermissionSlugConst::admin_question_store, "uri" => "/api/admin/question", "method" => "POST"],
                                    ["name" => "题库管理-修改", "slug" => PermissionSlugConst::admin_question_update, "uri" => "/api/admin/question/*", "method" => "PUT"],
                                    ["name" => "题库管理-删除", "slug" => PermissionSlugConst::admin_question_delete, "uri" => "/api/admin/question/*", "method" => "DELETE"],
                                ],
                            ],
                            // 知识点管理
                            [
                                'name'  => '知识点管理',
                                'items' => [
                                    ["name" => "知识点管理-查看", "slug" => PermissionSlugConst::admin_knowledge_show, "uri" => "/api/admin/knowledge/*", "method" => "GET"],
                                    ["name" => "知识点管理-列表", "slug" => PermissionSlugConst::admin_knowledge_list, "uri" => "/api/admin/knowledge", "method" => "GET"],
                                    ["name" => "知识点管理-新增", "slug" => PermissionSlugConst::admin_knowledge_store, "uri" => "/api/admin/knowledge", "method" => "POST"],
                                    ["name" => "知识点管理-修改", "slug" => PermissionSlugConst::admin_knowledge_update, "uri" => "/api/admin/knowledge/*", "method" => "PUT"],
                                    ["name" => "知识点管理-删除", "slug" => PermissionSlugConst::admin_knowledge_delete, "uri" => "/api/admin/knowledge/*", "method" => "DELETE"],
                                ],
                            ],
                            // 试卷管理
                            [
                                'name'  => '试卷管理',
                                'items' => [
                                    ["name" => "试卷管理-查看", "slug" => PermissionSlugConst::admin_paper_show, "uri" => "/api/admin/paper/*", "method" => "GET"],
                                    ["name" => "试卷管理-列表", "slug" => PermissionSlugConst::admin_paper_list, "uri" => "/api/admin/paper", "method" => "GET"],
                                    ["name" => "试卷管理-新增", "slug" => PermissionSlugConst::admin_paper_store, "uri" => "/api/admin/paper", "method" => "POST"],
                                    ["name" => "试卷管理-修改", "slug" => PermissionSlugConst::admin_paper_update, "uri" => "/api/admin/paper/*", "method" => "PUT"],
                                    ["name" => "试卷管理-删除", "slug" => PermissionSlugConst::admin_paper_delete, "uri" => "/api/admin/paper/*", "method" => "DELETE"],
                                    ["name" => "试卷管理-题目设置-列表", "slug" => PermissionSlugConst::admin_paper_question_list, "uri" => "/api/admin/paper_question", "method" => "GET"],
                                    ["name" => "试卷管理-题目设置-添加", "slug" => PermissionSlugConst::admin_paper_question_store, "uri" => "/api/admin/paper_question", "method" => "POST"],
                                    ["name" => "试卷管理-题目设置-删除", "slug" => PermissionSlugConst::admin_paper_question_delete, "uri" => "/api/admin/paper_question/*", "method" => "DELETE"],
                                    ["name" => "试卷管理-题目来源-列表", "slug" => PermissionSlugConst::admin_paper_question_source_list, "uri" => "/api/admin/paper_question_source", "method" => "GET"],
                                ],
                            ],
                        ]
                    ],
                    // 日常办公
                    [
                        "name"  => '日常办公',
                        "items" => [
                            // 工作任务类型
                            [
                                'name'  => '任务类型',
                                'items' => [
                                    ["name" => "任务类型-查看", "slug" => PermissionSlugConst::admin_work_task_type_show, "uri" => "/api/admin/work_task_type/*", "method" => "GET"],
                                    ["name" => "任务类型-列表", "slug" => PermissionSlugConst::admin_work_task_type_list, "uri" => "/api/admin/work_task_type", "method" => "GET"],
                                    ["name" => "任务类型-新增", "slug" => PermissionSlugConst::admin_work_task_type_store, "uri" => "/api/admin/work_task_type", "method" => "POST"],
                                    ["name" => "任务类型-修改", "slug" => PermissionSlugConst::admin_work_task_type_update, "uri" => "/api/admin/work_task_type/*", "method" => "PUT"],
                                    ["name" => "任务类型-删除", "slug" => PermissionSlugConst::admin_work_task_type_delete, "uri" => "/api/admin/work_task_type/*", "method" => "DELETE"],
                                ]
                            ],
                            // 工作任务
                            [
                                'name'  => '工作任务',
                                'items' => [
                                    ["name" => "工作任务-查看", "slug" => PermissionSlugConst::admin_work_task_show, "uri" => "/api/admin/work_task/*", "method" => "GET"],
                                    ["name" => "工作任务-列表", "slug" => PermissionSlugConst::admin_work_task_list, "uri" => "/api/admin/work_task", "method" => "GET"],
                                    ["name" => "工作任务-新增", "slug" => PermissionSlugConst::admin_work_task_store, "uri" => "/api/admin/work_task", "method" => "POST"],
                                    ["name" => "工作任务-修改", "slug" => PermissionSlugConst::admin_work_task_update, "uri" => "/api/admin/work_task/*", "method" => "PUT"],
                                    ["name" => "工作任务-删除", "slug" => PermissionSlugConst::admin_work_task_delete, "uri" => "/api/admin/work_task/*", "method" => "DELETE"],
                                ]
                            ],
                            // 工作任务上报
                            [
                                'name'  => '任务上报',
                                'items' => [
                                    ["name" => "任务上报-查看", "slug" => PermissionSlugConst::admin_work_task_report_show, "uri" => "/api/admin/work_task_report/*", "method" => "GET"],
                                    ["name" => "任务上报-列表", "slug" => PermissionSlugConst::admin_work_task_report_list, "uri" => "/api/admin/work_task_report", "method" => "GET"],
                                    ["name" => "任务上报-新增", "slug" => PermissionSlugConst::admin_work_task_report_store, "uri" => "/api/admin/work_task_report", "method" => "POST"],
                                    ["name" => "任务上报-修改", "slug" => PermissionSlugConst::admin_work_task_report_update, "uri" => "/api/admin/work_task_report/*", "method" => "PUT"],
                                    ["name" => "任务上报-删除", "slug" => PermissionSlugConst::admin_work_task_report_delete, "uri" => "/api/admin/work_task_report/*", "method" => "DELETE"],
                                ]
                            ],
                            // 通知 inform
                            [
                                'name'  => '通知',
                                'items' => [
                                    ["name" => "通知-查看", "slug" => PermissionSlugConst::admin_inform_show, "uri" => "/api/admin/inform/*", "method" => "GET"],
                                    ["name" => "通知-列表", "slug" => PermissionSlugConst::admin_inform_list, "uri" => "/api/admin/inform", "method" => "GET"],
                                    ["name" => "通知-新增", "slug" => PermissionSlugConst::admin_inform_store, "uri" => "/api/admin/inform", "method" => "POST"],
                                    ["name" => "通知-修改", "slug" => PermissionSlugConst::admin_inform_update, "uri" => "/api/admin/inform/*", "method" => "PUT"],
                                    ["name" => "通知-删除", "slug" => PermissionSlugConst::admin_inform_delete, "uri" => "/api/admin/inform/*", "method" => "DELETE"],
                                ]
                            ],
                            // 调查表
                            [
                                'name'  => '调查表',
                                'items' => [
                                    ["name" => "调查表-查看", "slug" => PermissionSlugConst::admin_survey_show, "uri" => "/api/admin/survey/*", "method" => "GET"],
                                    ["name" => "调查表-列表", "slug" => PermissionSlugConst::admin_survey_list, "uri" => "/api/admin/survey", "method" => "GET"],
                                    ["name" => "调查表-新增", "slug" => PermissionSlugConst::admin_survey_store, "uri" => "/api/admin/survey", "method" => "POST"],
                                    ["name" => "调查表-修改", "slug" => PermissionSlugConst::admin_survey_update, "uri" => "/api/admin/survey/*", "method" => "PUT"],
                                    ["name" => "调查表-删除", "slug" => PermissionSlugConst::admin_survey_delete, "uri" => "/api/admin/survey/*", "method" => "DELETE"],
                                    ["name" => '调查结果-分析', "slug" => PermissionSlugConst::admin_survey_statistics, "uri" => "/api/admin/survey/*/statistics", "method" => "GET"],
                                    ["name" => '调查表-小程序码', "slug" => PermissionSlugConst::admin_survey_qrcode, "uri" => "/api/admin/survey/*/qrcode", "method" => "POST"],


                                    ["name" => '调查结果-列表', "slug" => PermissionSlugConst::admin_survey_form_list, "uri" => "/api/admin/survey_form", "method" => "GET"],
                                    ["name" => '调查结果-查看', "slug" => PermissionSlugConst::admin_survey_form_show, "uri" => "/api/admin/survey_form/*", "method" => "GET"],
                                    ["name" => '调查结果-删除', "slug" => PermissionSlugConst::admin_survey_form_delete, "uri" => "/api/admin/survey_form/*", "method" => "DELETE"],
                                ]
                            ]
                        ]
                    ],

                    // 智能通知和回访
                    [
                        "name"  => '智能通知和回访',
                        "items" => [
                            [
                                'name'  => '短信模板',
                                'items' => [
                                    ["name" => "短信模板-查看", "slug" => PermissionSlugConst::admin_sms_template_show, "uri" => "/api/admin/sms_template/*", "method" => "GET"],
                                    ["name" => "短信模板-列表", "slug" => PermissionSlugConst::admin_sms_template_list, "uri" => "/api/admin/sms_template", "method" => "GET"],
                                    ["name" => "短信模板-新增", "slug" => PermissionSlugConst::admin_sms_template_store, "uri" => "/api/admin/sms_template", "method" => "POST"],
                                    ["name" => "短信模板-修改", "slug" => PermissionSlugConst::admin_sms_template_update, "uri" => "/api/admin/sms_template/*", "method" => "PUT"],
                                    ["name" => "短信模板-删除", "slug" => PermissionSlugConst::admin_sms_template_delete, "uri" => "/api/admin/sms_template/*", "method" => "DELETE"],
                                    ["name" => "短信模板-刷新审核状态", "slug" => PermissionSlugConst::admin_sms_template_refresh, "uri" => "/api/admin/sms_template/*/refresh", "method" => "POST"],
                                    ["name" => "短信模板-获取上传文件凭证", "slug" => PermissionSlugConst::admin_sms_upload_signature, "uri" => "/api/admin/get_sms_upload_signature", "method" => "GET"],
                                ],
                            ],
                            [
                                'name'  => '语音通知模板',
                                'items' => [
                                    ["name" => "语音通知模板-查看", "slug" => PermissionSlugConst::admin_vms_voice_template_show, "uri" => "/api/admin/vms_voice_template/*", "method" => "GET"],
                                    ["name" => "语音通知模板-列表", "slug" => PermissionSlugConst::admin_vms_voice_template_list, "uri" => "/api/admin/vms_voice_template", "method" => "GET"],
                                    ["name" => "语音通知模板-新增", "slug" => PermissionSlugConst::admin_vms_voice_template_store, "uri" => "/api/admin/vms_voice_template", "method" => "POST"],
                                    ["name" => "语音通知模板-修改", "slug" => PermissionSlugConst::admin_vms_voice_template_update, "uri" => "/api/admin/vms_voice_template/*", "method" => "PUT"],
                                    ["name" => "语音通知模板-删除", "slug" => PermissionSlugConst::admin_vms_voice_template_delete, "uri" => "/api/admin/vms_voice_template/*", "method" => "DELETE"],
                                ]
                            ],
                            [
                                'name'  => '机器人话术模板',
                                'items' => [
                                    ["name" => "机器人话术模板-查看", "slug" => PermissionSlugConst::admin_ccs_robot_template_show, "uri" => "/api/admin/ccs_robot_template/*", "method" => "GET"],
                                    ["name" => "机器人话术模板-列表", "slug" => PermissionSlugConst::admin_ccs_robot_template_list, "uri" => "/api/admin/ccs_robot_template", "method" => "GET"],
                                    ["name" => "机器人话术模板-新增", "slug" => PermissionSlugConst::admin_ccs_robot_template_store, "uri" => "/api/admin/ccs_robot_template", "method" => "POST"],
                                    ["name" => "机器人话术模板-修改", "slug" => PermissionSlugConst::admin_ccs_robot_template_update, "uri" => "/api/admin/ccs_robot_template/*", "method" => "PUT"],
                                    ["name" => "机器人话术模板-删除", "slug" => PermissionSlugConst::admin_ccs_robot_template_delete, "uri" => "/api/admin/ccs_robot_template/*", "method" => "DELETE"],
                                ]
                            ],
                            [
                                'name'  => '通知任务',
                                'items' => [
                                    ["name" => "智能通知任务-查看", "slug" => PermissionSlugConst::admin_out_notice_task_show, "uri" => "/api/admin/out_notice_task/*", "method" => "GET"],
                                    ["name" => "智能通知任务-列表", "slug" => PermissionSlugConst::admin_out_notice_task_list, "uri" => "/api/admin/out_notice_task", "method" => "GET"],
                                    ["name" => "智能通知任务-新增", "slug" => PermissionSlugConst::admin_out_notice_task_store, "uri" => "/api/admin/out_notice_task", "method" => "POST"],
                                    ["name" => "智能通知任务-修改", "slug" => PermissionSlugConst::admin_out_notice_task_update, "uri" => "/api/admin/out_notice_task/*", "method" => "PUT"],
                                    ["name" => "智能通知任务-删除", "slug" => PermissionSlugConst::admin_out_notice_task_delete, "uri" => "/api/admin/out_notice_task/*", "method" => "DELETE"],
                                    // 开始
                                    ["name" => "智能通知任务-开始", "slug" => PermissionSlugConst::admin_out_notice_task_start, "uri" => "/api/admin/out_notice_task/*/start", "method" => "POST"],
                                    // 停止
                                    ["name" => "智能通知任务-停止", "slug" => PermissionSlugConst::admin_out_notice_task_stop, "uri" => "/api/admin/out_notice_task/*/stop", "method" => "POST"],
                                    // 机器人外呼统计 out_notice_task_robot_statistics
                                    ["name" => "智能通知任务-机器人外呼-统计", "slug" => PermissionSlugConst::admin_out_notice_task_robot_statistics, "uri" => "/api/admin/out_notice_task/*/robot_statistics", "method" => "GET"],
                                ]
                            ],
                            [
                                'name'  => '通知接收人',
                                'items' => [
                                    ["name" => "通知接收人-查看", "slug" => PermissionSlugConst::admin_out_notice_task_receive_show, "uri" => "/api/admin/out_notice_task_receive/*", "method" => "GET"],
                                    ["name" => "通知接收人-列表", "slug" => PermissionSlugConst::admin_out_notice_task_receive_list, "uri" => "/api/admin/out_notice_task_receive", "method" => "GET"],
                                    ["name" => "通知接收人-新增", "slug" => PermissionSlugConst::admin_out_notice_task_receive_store, "uri" => "/api/admin/out_notice_task_receive", "method" => "POST"],
                                    ["name" => "通知接收人-修改", "slug" => PermissionSlugConst::admin_out_notice_task_receive_update, "uri" => "/api/admin/out_notice_task_receive/*", "method" => "PUT"],
                                    ["name" => "通知接收人-删除", "slug" => PermissionSlugConst::admin_out_notice_task_receive_delete, "uri" => "/api/admin/out_notice_task_receive/*", "method" => "DELETE"],
                                    // 清空
                                    ["name" => "通知接收人-清空", "slug" => PermissionSlugConst::admin_out_notice_task_receive_clear, "uri" => "/api/admin/out_notice_task_receive_clear", "method" => "POST"],
                                    // 批量运行
                                    ["name" => "通知接收人-选择批量运行", "slug" => PermissionSlugConst::admin_out_notice_task_receive_run, "uri" => "/api/admin/out_notice_task_receive_run", "method" => "POST"],
                                ]
                            ],
                        ],
                    ],
                    [
                        'name'  => '特殊时期征召',
                        'items' => [
                            [
                                'name'  => "特殊时期征召",
                                'items' => [
                                    ["name" => "特殊时期征召任务-列表", "slug" => PermissionSlugConst::admin_recall_task_list, "uri" => "/api/admin/recall_task", "method" => "GET"],
                                    ["name" => "特殊时期征召任务-详情", "slug" => PermissionSlugConst::admin_recall_task_show, "uri" => "/api/admin/recall_task/*", "method" => "GET"],
                                    ["name" => "特殊时期征召任务-新增", "slug" => PermissionSlugConst::admin_recall_task_store, "uri" => "/api/admin/recall_task", "method" => "POST"],
                                    ["name" => "特殊时期征召任务-修改", "slug" => PermissionSlugConst::admin_recall_task_update, "uri" => "/api/admin/recall_task/*", "method" => "PUT"],
                                    ["name" => "特殊时期征召任务-删除", "slug" => PermissionSlugConst::admin_recall_task_delete, "uri" => "/api/admin/recall_task/*", "method" => "DELETE"],
                                ]
                            ],
                            [
                                'name'  => "特殊时期征召-人员",
                                'items' => [
                                    ["name" => "特殊时期征召人员-列表", "slug" => PermissionSlugConst::admin_recall_task_soldier_list, "uri" => "/api/admin/recall_task_soldier", "method" => "GET"],
                                    ["name" => "特殊时期征召人员-详情", "slug" => PermissionSlugConst::admin_recall_task_soldier_show, "uri" => "/api/admin/recall_task_soldier/*", "method" => "GET"],
                                    ["name" => "特殊时期征召人员-新增", "slug" => PermissionSlugConst::admin_recall_task_soldier_store, "uri" => "/api/admin/recall_task_soldier", "method" => "POST"],
                                    ["name" => "特殊时期征召人员-修改", "slug" => PermissionSlugConst::admin_recall_task_soldier_update, "uri" => "/api/admin/recall_task_soldier/*", "method" => "PUT"],
                                    ["name" => "特殊时期征召人员-删除", "slug" => PermissionSlugConst::admin_recall_task_soldier_delete, "uri" => "/api/admin/recall_task_soldier/*", "method" => "DELETE"],
                                    ["name" => "特殊时期征召人员-全部清除", "slug" => PermissionSlugConst::admin_recall_task_soldier_clear, "uri" => "/api/admin/recall_task_soldier_clear", "method" => "POST"],
                                    ['name' => '特殊时期征召人员-搜索项', "slug" => PermissionSlugConst::admin_recall_task_soldier_search_option, "uri" => "/api/admin/recall_task_soldier_search_option", "method" => "GET"],
                                ]
                            ],
                        ]
                    ],
                    [
                        'name'  => '统计',
                        'items' => [
                            [
                                'name'  => '课程统计',
                                'items' => [
                                    ["name" => "课程学习进度统计", "slug" => PermissionSlugConst::admin_statistics_course_list, "uri" => "/api/admin/statistics_course", "method" => "GET"],
                                    ["name" => "人员学习进度统计", "slug" => PermissionSlugConst::admin_statistics_user_course_list, "uri" => "/api/admin/statistics_user_course", "method" => "GET"],
                                    ["name" => '部门学习进度统计', "slug" => PermissionSlugConst::admin_statistics_department_course_list, "uri" => "/api/admin/statistics_department_course", "method" => "GET"]
                                ]
                            ],
                            [
                                'name'  => '考核统计',
                                'items' => [
                                    ["name" => '人员考核统计', "slug" => PermissionSlugConst::admin_statistics_user_paper_round_list, "uri" => "/api/admin/statistics_user_paper_round", "method" => "GET"],
                                    ["name" => '部门考核统计', "slug" => PermissionSlugConst::admin_statistics_department_paper_round_list, "uri" => "/api/admin/statistics_department_paper_round", "method" => "GET"],
                                ]
                            ],
//                            [
//                                'name'  => '人员统计',
//                                'items' => [
//                                    // 人员积分统计
//                                    ["name" => '人员积分统计', "slug" => PermissionSlugConst::admin_statistics_user_point_list, "uri" => "/api/admin/statistics_user_point", "method" => "GET"],
//                                    // 拨打电话统计
//                                    ["name" => '拨打电话统计', "slug" => PermissionSlugConst::admin_statistics_user_call_list, "uri" => "/api/admin/statistics_user_call", "method" => "GET"],
//                                ],
//                            ],
                            [
                                'name'  => '面板统计',
                                'items' => [
                                    ["name" => '用户访问趋势统计', "slug" => PermissionSlugConst::admin_statistics_user_visit, "uri" => "/api/admin/statistics_user_visit", "method" => "GET"],
                                    ["name" => "各项数据量统计", "slug" => PermissionSlugConst::admin_statistics_data_count, "uri" => "/api/admin/statistics_data_count", "method" => "GET"],
                                ],
                            ],
                            [
                                'name'  => '领导驾驶舱',
                                'items' => [
                                    ["name" => "领导驾驶舱-整体数据", "slug" => PermissionSlugConst::admin_statistics_leader_big_data, "uri" => "/api/admin/statistics_leader_big_data", "method" => "GET"],
                                    ["name" => "领导驾驶舱-区县数据", "slug" => PermissionSlugConst::admin_statistics_leader_big_data_district, "uri" => "/api/admin/admin_statistics_leader_big_data_district", "method" => "GET"],
                                ]
                            ]
                        ]
                    ],
                    [
                        'name'  => '平时工作',
                        'items' => [
                            [
                                'name'  => '平时任务',
                                'items' => [
                                    ["name" => '任务列表', 'slug' => PermissionSlugConst::admin_mission_list, "uri" => '/api/admin/mission', "method" => "GET"],
                                    ["name" => '任务详情', 'slug' => PermissionSlugConst::admin_mission_show, "uri" => '/api/admin/mission/*', "method" => "GET"],
                                    ["name" => '任务创建', 'slug' => PermissionSlugConst::admin_mission_store, "uri" => '/api/admin/mission', "method" => "POST"],
                                    ["name" => '任务更新', 'slug' => PermissionSlugConst::admin_mission_update, "uri" => '/api/admin/mission/*', "method" => "PUT"],
                                    ["name" => '任务删除', 'slug' => PermissionSlugConst::admin_mission_delete, "uri" => '/api/admin/mission/*', "method" => "DELETE"],
                                    ["name" => "任务详情统计", "slug" => PermissionSlugConst::admin_statistics_mission_detail, "uri" => "/api/admin/statistics/mission_detail", "method" => "GET"]
                                ],
                            ],
                            // 人员列表
                            [
                                "name"  => '人员列表',
                                'items' => [
                                    ['name' => '人员列表', 'slug' => PermissionSlugConst::admin_mission_person_list, 'uri' => '/api/admin/mission_person', 'method' => 'GET'],
                                    ['name' => '人员详情', 'slug' => PermissionSlugConst::admin_mission_person_show, 'uri' => '/api/admin/mission_person/*', 'method' => 'GET'],
                                    ['name' => '新增人员', 'slug' => PermissionSlugConst::admin_mission_person_store, 'uri' => '/api/admin/mission_person', 'method' => 'POST'],
                                    ['name' => '修改人员', 'slug' => PermissionSlugConst::admin_mission_person_update, 'uri' => '/api/admin/mission_person/*', 'method' => 'PUT'],
                                    ['name' => '删除人员', 'slug' => PermissionSlugConst::admin_mission_person_delete, 'uri' => '/api/admin/mission_person/*', 'method' => 'DELETE'],

                                    // 统计数据
                                    ["name" => "高校人员-统计数据", "slug" => PermissionSlugConst::admin_statistics_mission_school_person_list, "uri" => "/api/admin/statistics/mission_school_person_list", "method" => "GET"],
                                    ["name" => "高校人员-统计图", "slug" => PermissionSlugConst::admin_statistics_mission_school_person_chart, "uri" => "/api/admin/statistics/mission_school_person_chart", "method" => "GET"],
                                    // 统计数据
                                    ["name" => "社会适龄青年-统计数据", "slug" => PermissionSlugConst::admin_statistics_mission_social_person_list, "uri" => "/api/admin/statistics/mission_social_person_list", "method" => "GET"],
                                    ["name" => "社会适龄青年-统计图", "slug" => PermissionSlugConst::admin_statistics_mission_social_person_chart, "uri" => "/api/admin/statistics/mission_social_person_chart", "method" => "GET"],
                                ]
                            ],
                            // 任务数设置
                            [
                                "name"  => "任务数设置",
                                "items" => [
                                    ["name" => "任务数详情", "slug" => PermissionSlugConst::admin_mission_task_num_show, "uri" => "/api/admin/mission_task_num/*", "method" => "GET"],
                                    ["name" => "任务数修改", "slug" => PermissionSlugConst::admin_mission_task_num_update, "uri" => "/api/admin/mission_task_num/*", "method" => "PUT"],
                                    ["name" => "任务数列表", "slug" => PermissionSlugConst::admin_mission_task_num_list, "uri" => "/api/admin/mission_task_num", "method" => "GET"],
                                    // 统计数据
                                    ["name" => "任务数统计", "slug" => PermissionSlugConst::admin_statistics_mission_task_num_list, "uri" => "/api/admin/statistics/mission_task_num_list", "method" => "GET"],
                                ],
                            ],
                            // 意向统计
                            [
                                "name"  => "意向统计",
                                "items" => [
                                    ["name" => "标记意向", "slug" => PermissionSlugConst::admin_mission_intention_mark_intention, "uri" => "/api/admin/mission_person_mark_intention", "method" => "POST"],
                                    ["name" => "数据汇总", "slug" => PermissionSlugConst::admin_statistics_mission_intention_list, "uri" => "/api/admin/statistics/mission_intention_list", "method" => "GET"]
                                ]
                            ],
                            // 宣传对比
                            [
                                "name"  => "宣传对比",
                                "items" => [
                                    ["name" => "报名列表", "slug" => PermissionSlugConst::admin_mission_sign_person_list, "uri" => "/api/admin/mission_sign_person", "method" => "GET"],
                                    ["name" => "报名详情", "slug" => PermissionSlugConst::admin_mission_sign_person_show, "uri" => "/api/admin/mission_sign_person/*", "method" => "GET"],
                                    ["name" => "报名添加", "slug" => PermissionSlugConst::admin_mission_sign_person_store, "uri" => "/api/admin/mission_sign_person", "method" => "POST"],
                                    ["name" => "报名修改", "slug" => PermissionSlugConst::admin_mission_sign_person_update, "uri" => "/api/admin/mission_sign_person/*", "method" => "PUT"],
                                    ["name" => "报名删除", "slug" => PermissionSlugConst::admin_mission_sign_person_delete, "uri" => "/api/admin/mission_sign_person/*", "method" => "DELETE"],
                                    ["name" => "报名追加人员库", "slug" => PermissionSlugConst::admin_mission_sign_person_append, "uri" => "/api/admin/mission_sign_person_append", "method" => "POST"],
                                    ["name" => "对比结果", "slug" => PermissionSlugConst::admin_mission_promote_compare_list, "uri" => "/api/admin/mission_promote_compare", "method" => "GET"],
                                    ["name" => "数据汇总", "slug" => PermissionSlugConst::admin_statistics_mission_promote_compare_list, "uri" => "/api/admin/statistics/mission_promote_compare_list", "method" => "GET"],
                                ]
                            ],
                            // 体格检查
                            [
                                "name"  => "体格检查",
                                "items" => [
                                    ["name" => "上站率设置-保存", "slug" => PermissionSlugConst::admin_mission_physical_examination_station_rate_store, "uri" => "/api/admin/mission_physical_examination_station_rate", "method" => "POST"],
                                    ["name" => "上站率设置-显示", "slug" => PermissionSlugConst::admin_mission_physical_examination_station_rate_show, "uri" => "/api/admin/mission_physical_examination_station_rate", "method" => "GET"],

                                    ["name" => "计划上报-列表", "slug" => PermissionSlugConst::admin_mission_physical_examination_list, "uri" => "/api/admin/mission_physical_examination", "method" => "GET"],
                                    ["name" => "计划上报-详情", "slug" => PermissionSlugConst::admin_mission_physical_examination_show, "uri" => "/api/admin/mission_physical_examination/*", "method" => "GET"],
                                    ["name" => "计划上报-保存", "slug" => PermissionSlugConst::admin_mission_physical_examination_store, "uri" => "/api/admin/mission_physical_examination", "method" => "POST"],
                                    ["name" => "计划上报-编辑", "slug" => PermissionSlugConst::admin_mission_physical_examination_update, "uri" => "/api/admin/mission_physical_examination/*", "method" => "PUT"],
                                    ["name" => "计划上报-删除", "slug" => PermissionSlugConst::admin_mission_physical_examination_delete, "uri" => "/api/admin/mission_physical_examination/*", "method" => "DELETE"],
                                    ["name" => "计划上报-撤回", "slug" => PermissionSlugConst::admin_mission_physical_examination_recall, "uri" => "/api/admin/mission_physical_examination_recall", "method" => "POST"],
                                    ["name" => "计划上报-提交", "slug" => PermissionSlugConst::admin_mission_physical_examination_submit, "uri" => "/api/admin/mission_physical_examination_submit", "method" => "POST"],
                                    ["name" => "体检名单", "slug" => PermissionSlugConst::admin_mission_physical_examination_person_list, "uri" => "/api/admin/mission_physical_examination_person_list", "method" => "GET"],
                                    ["name" => "体检名单-删除", "slug" => PermissionSlugConst::admin_mission_physical_examination_person_delete, "uri" => "/api/admin/mission_physical_examination_person_delete", "method" => "DELETE"],
                                    // 设置体检结果
                                    ["name" => "体检-标记结果", "slug" => PermissionSlugConst::admin_mission_physical_examination_mark_result, "uri" => "/api/admin/mission_physical_examination_mark_result", "method" => "POST"],
                                    // 设置复查结果
                                    ["name" => "复查-标记结果", "slug" => PermissionSlugConst::admin_mission_physical_examination_mark_recheck_result, "uri" => "/api/admin/mission_physical_examination_mark_recheck_result", "method" => "POST"],
                                    // 设置抽查结果
                                    ["name" => "抽查-标记结果", "slug" => PermissionSlugConst::admin_mission_physical_examination_mark_spot_check_result, "uri" => "/api/admin/mission_physical_examination_mark_spot_check_result", "method" => "POST"],
                                    ["name" => "抽查-人员列表", "slug" => PermissionSlugConst::admin_mission_physical_examination_spot_check_person_list, "uri" => "", "method" => "GET"],

                                    ["name" => "数据汇总-任务明细", "slug" => PermissionSlugConst::admin_statistics_mission_physical_task_num_list, "uri" => "/api/admin/statistics/mission_physical_task_num_list", "method" => "GET"],
                                    ["name" => "数据汇总-体检结果", "slug" => PermissionSlugConst::admin_statistics_mission_physical_check_list, "uri" => "/api/admin/statistics/mission_physical_check_list", "method" => "GET"],
                                    ["name" => "数据汇总-复查结果", "slug" => PermissionSlugConst::admin_statistics_mission_physical_recheck_list, "uri" => "/api/admin/statistics/mission_physical_recheck_list", "method" => "GET"],
                                    ["name" => "数据汇总-抽查结果", "slug" => PermissionSlugConst::admin_statistics_mission_physical_spot_check_list, "uri" => "/api/admin/statistics/mission_physical_spot_check_list", "method" => "GET"],
                                ]
                            ],
                            // 政考
                            [
                                "name"  => "政考",
                                "items" => [
                                    ["name" => "政考-人员列表", "slug" => PermissionSlugConst::admin_mission_political_exam_person_list, "uri" => "/api/admin/mission_political_exam_person", "method" => "GET"],
                                    ["name" => "数据汇总", "slug" => PermissionSlugConst::admin_statistics_mission_political_exam_list, "uri" => "/api/admin/statistics/mission_political_exam_list", "method" => "GET"]
                                ]
                            ],
                            // 役前教育
                            [
                                "name"  => "役前教育",
                                "items" => [
                                    ["name" => "预储-人员列表", "slug" => PermissionSlugConst::admin_mission_education_person_list, "uri" => "/api/admin/mission_education_person", "method" => "GET"],
                                    ["name" => "数据汇总", "slug" => PermissionSlugConst::admin_statistics_mission_education_list, "uri" => "/api/admin/statistics/mission_education_list", "method" => "GET"]
                                ]
                            ],
                            [
                                "name"  => "走兵",
                                "items" => [
                                    ["name" => "数据汇总", "slug" => PermissionSlugConst::admin_statistics_mission_go_list, "uri" => "/api/admin/statistics/mission_go_list", "method" => "GET"]
                                ]
                            ],
                            // 预储
                            [
                                "name"  => "预储",
                                "items" => [
                                    ["name" => "预储-人员列表", "slug" => PermissionSlugConst::admin_mission_pre_store_person_list, "uri" => "/api/admin/mission_pre_store_person", "method" => "GET"],
                                    ["name" => "数据汇总", "slug" => PermissionSlugConst::admin_statistics_mission_pre_store_list, "uri" => "/api/admin/statistics/mission_pre_store_list", "method" => "GET"]
                                ]
                            ],
                            // 数据汇总
                            [
                                "name"  => "数据汇总",
                                "items" => [
                                    ["name" => "数据汇总", "slug" => PermissionSlugConst::admin_statistics_mission_summary_list, "uri" => "/api/admin/statistics/mission_summary_list", "method" => "GET"]
                                ],
                            ],
                        ]
                    ],
                    [
                        'name'  => '系统通知',
                        'items' => [
                            [
                                'name'  => "系统通知",
                                'items' => [
                                    ["name" => "系统通知-列表", "slug" => PermissionSlugConst::admin_system_notification_list, "uri" => "/api/admin/notification", "method" => "GET"],
                                    ["name" => "系统通知-详情", "slug" => PermissionSlugConst::admin_system_notification_show, "uri" => "/api/admin/notification/*", "method" => "GET"],
                                    ["name" => "系统通知-标记已读", "slug" => PermissionSlugConst::admin_system_notification_read, "uri" => "/api/admin/notification_read/*", "method" => "POST"],
                                ]
                            ]
                        ]
                    ],
                    [
                        "name"  => '其他',
                        "items" => [
                            [
                                'name'  => '文件',
                                'items' => [
                                    ["name" => "文件-查看", "slug" => PermissionSlugConst::admin_file_show, "uri" => "/api/admin/get_file", "method" => "POST"],
                                    ["name" => "文件-上传", "slug" => PermissionSlugConst::admin_file_upload, "uri" => "/api/admin/upload_file", "method" => "POST"],
                                    // 文件夹
                                    ["name" => "文件夹-查看", "slug" => PermissionSlugConst::admin_folder_show, "uri" => "/api/admin/folder/*", "method" => "GET"],
                                    ["name" => "文件夹-列表", "slug" => PermissionSlugConst::admin_folder_list, "uri" => "/api/admin/folder", "method" => "GET"],
                                    ["name" => "文件夹-新增", "slug" => PermissionSlugConst::admin_folder_store, "uri" => "/api/admin/folder", "method" => "POST"],
                                    ["name" => "文件夹-修改", "slug" => PermissionSlugConst::admin_folder_update, "uri" => "/api/admin/folder/*", "method" => "PUT"],
                                    ["name" => "文件夹-删除", "slug" => PermissionSlugConst::admin_folder_delete, "uri" => "/api/admin/folder/*", "method" => "DELETE"],
                                    // 文件列表
                                    ["name" => "文件列表", "slug" => PermissionSlugConst::admin_file_list, "uri" => "/api/admin/files", "method" => "GET"],
                                    // 添加文件
                                    ["name" => "添加文件", "slug" => PermissionSlugConst::admin_file_add, "uri" => "/api/admin/add_file", "method" => "POST"],
                                    ["name" => "删除文件", "slug" => PermissionSlugConst::admin_file_delete, "uri" => "/api/admin/file", "method" => "DELETE"]
                                ]
                            ],
                            [
                                'name'  => '三方接口',
                                'items' => [
                                    ["name" => "阿里云oss", "slug" => PermissionSlugConst::admin_huawei_security_token, "uri" => "/api/admin/huawei_security_token", "method" => "*"],
                                    ["name" => "华为云obs", "slug" => PermissionSlugConst::admin_alibaba_security_token, "uri" => "/api/admin/alibaba_security_token", "method" => "*"],
                                ]
                            ],
                            [
                                'name'  => '（导入、导出数据）',
                                'items' => [
                                    ["name" => "任务-列表", "slug" => PermissionSlugConst::admin_task_list, "uri" => "/api/admin/task", "method" => "GET"],
                                    ["name" => "任务-新增", "slug" => PermissionSlugConst::admin_task_store, "uri" => "/api/admin/task", "method" => "POST"],
                                    ["name" => "任务-导入数据列表", "slug" => PermissionSlugConst::admin_import_item_list, "uri" => "/api/admin/import_item", "method" => "GET"],
                                    ["name" => "任务-导入数据查看", "slug" => PermissionSlugConst::admin_import_item_show, "uri" => "/api/admin/import_item/*", "method" => "GET"],
                                    ["name" => "任务-导入数据修改", "slug" => PermissionSlugConst::admin_import_item_update, "uri" => "/api/admin/import_item/*", "method" => "PUT"],
                                    // 确认导入
                                    ["name" => "任务-确认导入", "slug" => PermissionSlugConst::admin_task_confirm, "uri" => "/api/admin/task_confirm", "method" => "POST"],
                                ]
                            ],
                            [
                                "name"  => '操作记录',
                                "items" => [
                                    ["name" => "操作记录-列表", "slug" => PermissionSlugConst::admin_operation_log_list, "uri" => "/api/admin/operation_log", "method" => "GET"],
                                    // 详情
                                    ["name" => "操作记录-详情", "slug" => PermissionSlugConst::admin_operation_log_show, "uri" => "/api/admin/operation_log/*", "method" => "GET"],
                                ]
                            ]
                        ]
                    ],
                ]
            ],
            [
                "name"  => "小程序端",
                "items" => [
                    [
                        "name"  => "账号",
                        "items" => [
                            [
                                'name'  => '个人账号',
                                'items' => [
                                    ["name" => "账号信息", "slug" => PermissionSlugConst::mini_user_info, "uri" => "/api/mini/info", "method" => "GET"],
                                    ["name" => "修改信息", "slug" => PermissionSlugConst::mini_user_info_update, "uri" => "/api/mini/info", "method" => "POST"],
                                    ["name" => "修改密码", "slug" => PermissionSlugConst::mini_user_password_update, "uri" => "/api/mini/update_password", "method" => "POST"],
                                    ["name" => '人脸验证-获取useridkey', "slug" => PermissionSlugConst::mini_get_user_id_key, "uri" => "/api/mini/get_user_id_key", "method" => "GET"],
                                    ["name" => '人脸验证-人脸验证', "slug" => PermissionSlugConst::mini_get_user_face_result, "uri" => "/api/mini/get_user_face_result", "method" => "GET"],
                                    // 账号激活
                                    ["name" => '账号激活', "slug" => PermissionSlugConst::mini_user_active, "uri" => "/api/mini/account_activate", "method" => "POST"],
                                ],
                            ],
                        ]
                    ],
                    // 课程列表
                    [
                        "name"  => "课程",
                        "items" => [
                            [
                                'name'  => '课程',
                                'items' => [
                                    ["name" => "课程列表", "slug" => PermissionSlugConst::mini_course_list, "uri" => "/api/mini/course", "method" => "GET"],
                                    ["name" => "课程-进度上传", "slug" => PermissionSlugConst::mini_course_upload_course_process, "uri" => "/api/mini/upload_course_process", "method" => "POST"],
                                    ["name" => "章节列表", "slug" => PermissionSlugConst::mini_chapter_list, "uri" => "/api/mini/chapter", "method" => "GET"],
                                    ["name" => "章节详情", "slug" => PermissionSlugConst::mini_chapter_show, "uri" => "/api/mini/chapter/*", "method" => "GET"],

                                ]
                            ],
                            [
                                'name'  => '练习测试',
                                'items' => [
                                    ["name" => "练习测试-详情", "slug" => PermissionSlugConst::mini_paper_show, "uri" => "/api/mini/paper/*", "method" => "GET"],
                                    ["name" => "练习测试-开始答题", "slug" => PermissionSlugConst::mini_paper_start, "uri" => "/api/mini/paper/*/start", "method" => "POST"],
                                    ["name" => "练习测试-上一题", "slug" => PermissionSlugConst::mini_paper_prev_question, "uri" => "/api/mini/paper/*/pre_question", "method" => "POST"],
                                    ["name" => "练习测试-下一题", "slug" => PermissionSlugConst::mini_paper_next_question, "uri" => "/api/mini/paper/*/next_question", "method" => "POST"],
                                    ["name" => "练习测试-答题", "slug" => PermissionSlugConst::mini_paper_answer, "uri" => "/api/mini/paper/*/answer", "method" => "POST"],
                                    ["name" => "练习测试-完成提交", "slug" => PermissionSlugConst::mini_paper_finish, "uri" => "/api/mini/paper/*/finish", "method" => "POST"],
                                    ["name" => "查看是否有正式考核", "slug" => PermissionSlugConst::mini_exam_paper, "uri" => "/api/mini/exam_paper", "method" => "GET"],
                                    ["name" => "做题记录", "slug" => PermissionSlugConst::mini_paper_question_list, "uri" => "/api/mini/paper/*/questions", "method" => "GET"],
                                ],
                            ],
                            [
                                'name'  => '错题本',
                                'items' => [
                                    ["name" => "错题列表", "slug" => PermissionSlugConst::mini_error_question_list, "uri" => "/api/mini/error_question", "method" => "GET"],
                                    // 删除
                                    ["name" => "删除错题", "slug" => PermissionSlugConst::mini_error_question_delete, "uri" => "/api/mini/error_question/*", "method" => "DELETE"],
                                    ["name" => "开始答题", "slug" => PermissionSlugConst::mini_error_question_start, "uri" => "/api/mini/error_question_start", "method" => "POST"],
                                    ["name" => "下一题", "slug" => PermissionSlugConst::mini_error_question_next_question, "uri" => "/api/mini/error_question/*/next_question", "method" => "POST"],
                                    ["name" => "上一题", "slug" => PermissionSlugConst::mini_error_question_prev_question, "uri" => "/api/mini/error_question/*/pre_question", "method" => "POST"],
                                    ["name" => "答题", "slug" => PermissionSlugConst::mini_error_question_answer, "uri" => "/api/mini/error_question/*/answer", "method" => "POST"],
                                    ["name" => "完成提交", "slug" => PermissionSlugConst::mini_error_question_finish, "uri" => "/api/mini/error_question/*/finish", "method" => "POST"],
                                    // 题目详情
                                    ["name" => "题目详情", "slug" => PermissionSlugConst::mini_question_show, "uri" => "/api/mini/question/*", "method" => "GET"],
                                ],
                            ],
                        ],
                    ],
                    // 咨询
                    [
                        'name'  => '咨询',
                        'items' => [
                            [
                                'name'  => '咨询',
                                'items' => [
                                    ["name" => "咨询-相关内容", "slug" => PermissionSlugConst::mini_consult_article_list, "uri" => "/api/mini/consult_article", "method" => "*"],
                                ]
                            ]
                        ]
                    ],
                    // 收藏
                    [
                        'name'  => '我的',
                        'items' => [
                            [
                                'name'  => '收藏',
                                'items' => [
                                    ["name" => "收藏-列表", "slug" => PermissionSlugConst::mini_my_collect, "uri" => "/api/mini/collect", "method" => "GET"],
                                    ["name" => "收藏-新增", "slug" => PermissionSlugConst::mini_collect, "uri" => "/api/mini/collect", "method" => "POST"],
                                ]
                            ],
                            // 证书
                            [
                                'name'  => '证书',
                                'items' => [
                                    ["name" => "证书-列表", "slug" => PermissionSlugConst::mini_certificate_list, "uri" => "/api/mini/certificate", "method" => "GET"],
                                    ["name" => "证书-详情", "slug" => PermissionSlugConst::mini_certificate_show, "uri" => "/api/mini/certificate/*", "method" => "GET"],
                                ]
                            ],
                            [
                                'name'  => "积分",
                                'items' => [
                                    ["name" => "积分-明细", "slug" => PermissionSlugConst::mini_point_list, "uri" => "/api/mini/point", "method" => "GET"],
                                    ["name" => "积分-排名", "slug" => PermissionSlugConst::mini_point_rank, "uri" => "/api/mini/point_rank", "method" => "GET"],
                                ]
                            ]
                        ],
                    ],
                    // 日常办公
                    [
                        'name'  => "日常办公",
                        'items' => [
                            [
                                'name'  => "工作任务",
                                'items' => [
                                    // 任务类型
                                    ["name" => "任务类型-列表", "slug" => PermissionSlugConst::mini_work_task_type_list, "uri" => "/api/mini/work_task_type", "method" => "GET"],
                                    ["name" => "工作任务-列表", "slug" => PermissionSlugConst::mini_work_task_list, "uri" => "/api/mini/work_task", "method" => "GET"],
                                    ["name" => "工作任务-详情", "slug" => PermissionSlugConst::mini_work_task_show, "uri" => "/api/mini/work_task/*", "method" => "GET"],
                                    ["name" => "工作任务-新增", "slug" => PermissionSlugConst::mini_work_task_store, "uri" => "/api/mini/work_task", "method" => "POST"],
                                    ["name" => "工作任务-修改", "slug" => PermissionSlugConst::mini_work_task_update, "uri" => "/api/mini/work_task/*", "method" => "PUT"],
                                    ["name" => "工作任务-删除", "slug" => PermissionSlugConst::mini_work_task_delete, "uri" => "/api/mini/work_task/*", "method" => "DELETE"],
                                ]
                            ],
                            [
                                'name'  => "任务上报",
                                'items' => [
                                    ["name" => "任务上报-列表", "slug" => PermissionSlugConst::mini_work_task_report_list, "uri" => "/api/mini/work_task_report", "method" => "GET"],
                                    ["name" => "任务上报-详情", "slug" => PermissionSlugConst::mini_work_task_report_show, "uri" => "/api/mini/work_task_report/*", "method" => "GET"],
                                    ["name" => "任务上报-新增", "slug" => PermissionSlugConst::mini_work_task_report_store, "uri" => "/api/mini/work_task_report", "method" => "POST"],
                                    ["name" => "任务上报-修改", "slug" => PermissionSlugConst::mini_work_task_report_update, "uri" => "/api/mini/work_task_report/*", "method" => "PUT"],
                                    ["name" => "任务上报-删除", "slug" => PermissionSlugConst::mini_work_task_report_delete, "uri" => "/api/mini/work_task_report/*", "method" => "DELETE"],
                                ],
                            ],
                            // 通知
                            [
                                'name'  => "通知",
                                'items' => [
                                    ["name" => "通知-列表", "slug" => PermissionSlugConst::mini_inform_list, "uri" => "/api/mini/inform", "method" => "GET"],
                                    ["name" => "通知-详情", "slug" => PermissionSlugConst::mini_inform_show, "uri" => "/api/mini/inform/*", "method" => "GET"],
                                    ["name" => "通知-新增", "slug" => PermissionSlugConst::mini_inform_store, "uri" => "/api/mini/inform", "method" => "POST"],
                                    ["name" => "通知-修改", "slug" => PermissionSlugConst::mini_inform_update, "uri" => "/api/mini/inform/*", "method" => "PUT"],
                                    ["name" => "通知-删除", "slug" => PermissionSlugConst::mini_inform_delete, "uri" => "/api/mini/inform/*", "method" => "DELETE"],
                                ]
                            ],
                        ]
                    ],
                    [
                        'name'  => '特殊时期征召',
                        'items' => [
                            [
                                'name'  => '征招任务',
                                'items' => [
                                    ["name" => "征招任务-列表", "slug" => PermissionSlugConst::mini_recall_task_list, "uri" => "/api/mini/recall_task", "method" => "GET"],
                                    ["name" => "征招任务-详情", "slug" => PermissionSlugConst::mini_recall_task_show, "uri" => "/api/mini/recall_task/*", "method" => "GET"],
                                ]
                            ],
                            [
                                'name'  => '征招人员',
                                'items' => [
                                    ["name" => "征招人员-列表", "slug" => PermissionSlugConst::mini_recall_task_soldier_list, "uri" => "/api/mini/recall_task_soldier", "method" => "GET"],
                                    ["name" => "征招人员-详情", "slug" => PermissionSlugConst::mini_recall_task_soldier_show, "uri" => "/api/mini/recall_task_soldier/*", "method" => "GET"],
                                    ["name" => "征招人员-阶段完成", "slug" => PermissionSlugConst::mini_recall_task_soldier_stage_finish, "uri" => "/api/mini/recall_task_soldier_stage_finish/*", "method" => "POST"]
                                ]
                            ]
                        ]
                    ],
                    [
                        'name'  => '平时工作',
                        'items' => [
                            //平时任务
                            [
                                'name'  => '平时任务',
                                'items' => [
                                    ["name" => "平时任务-列表", "slug" => PermissionSlugConst::mini_mission_list, "uri" => "/api/mini/mission", "method" => "GET"],
                                    ["name" => "平时任务-详情", "slug" => PermissionSlugConst::mini_mission_show, "uri" => "/api/mini/mission/*", "method" => "GET"],
                                ]
                            ],
                            //平时任务人员
                            [
                                'name'  => '平时任务人员',
                                'items' => [
                                    ["name" => "平时任务人员-列表", "slug" => PermissionSlugConst::mini_mission_person_list, "uri" => "/api/mini/mission_person", "method" => "GET"],
                                    ["name" => "平时任务人员-详情", "slug" => PermissionSlugConst::mini_mission_person_show, "uri" => "/api/mini/mission_person/*", "method" => "GET"],
                                    ["name" => "平时任务人员-修改", "slug" => PermissionSlugConst::mini_mission_person_update, "uri" => "/api/mini/mission_person/*", "method" => "PUT"],
                                ]
                            ],
                            // 体检计划
                            [
                                'name'  => '体检计划',
                                'items' => [
                                    ["name" => "体检计划-列表", "slug" => PermissionSlugConst::mini_mission_physical_examination_list, "uri" => "/api/mini/mission_physical_examination", "method" => "GET"],
                                    ["name" => "体检计划人员-列表", "slug" => PermissionSlugConst::mini_mission_physical_examination_person_list, "uri" => "/api/mini/mission_physical_examination_person_list", "method" => "GET"],
                                ]
                            ]
                        ]
                    ],
                    [
                        'name'  => '系统通知',
                        'items' => [
                            [
                                'name'  => "系统通知",
                                'items' => [
                                    ["name" => "系统通知-列表", "slug" => PermissionSlugConst::mini_system_notification_list, "uri" => "/api/mini/notification", "method" => "GET"],
                                    ["name" => "系统通知-详情", "slug" => PermissionSlugConst::mini_system_notification_show, "uri" => "/api/mini/notification/*", "method" => "GET"],
                                    ["name" => "系统通知-标记已读", "slug" => PermissionSlugConst::mini_system_notification_read, "uri" => "/api/mini/notification_read/*", "method" => "POST"],
                                ]
                            ]
                        ]
                    ],
                    // 统计
                    [
                        'name'  => "统计",
                        'items' => [
                            [
                                'name'  => "统计",
                                'items' => [
                                    // 学习进度统计
                                    ["name" => "学习进度统计-列表", "slug" => PermissionSlugConst::mini_statistics_course_list, "uri" => "/api/mini/statistics/course", "method" => "GET"],
                                    // 考核统计
                                    ["name" => "考核统计-列表", "slug" => PermissionSlugConst::mini_statistics_paper_list, "uri" => "/api/mini/statistics/paper", "method" => "GET"],
                                    // 考核统计-成绩
                                    ["name" => "考核统计-成绩-列表", "slug" => PermissionSlugConst::mini_statistics_paper_result_list, "uri" => "/api/mini/statistics/paper_result", "method" => "GET"],
                                    // 工作任务统计
                                    ["name" => "工作任务统计-列表", "slug" => PermissionSlugConst::mini_statistics_work_task_list, "uri" => "/api/mini/statistics/work_task", "method" => "GET"],
                                    // 征招任务统计
                                    ["name" => "征招任务统计-列表", "slug" => PermissionSlugConst::mini_statistics_recall_task_list, "uri" => "/api/mini/statistics/recall_task", "method" => "GET"],
                                    // 回访任务统计
                                    ["name" => "回访任务统计-列表", "slug" => PermissionSlugConst::mini_statistics_out_notice_task_list, "uri" => "/api/mini/statistics/out_notice_task", "method" => "GET"],
                                    // 学校人员统计
                                    ["name" => "学校人员统计-列表", "slug" => PermissionSlugConst::mini_statistics_mission_school_person_list, "uri" => "/api/mini/statistics/mission_school_person_list", "method" => "GET"],
                                    // 社会人员统计
                                    ["name" => "社会人员统计-列表", "slug" => PermissionSlugConst::mini_statistics_mission_social_person_list, "uri" => "/api/mini/statistics/mission_social_person_list", "method" => "GET"],
                                    // 意向统计
                                    ["name" => "意向统计-列表", "slug" => PermissionSlugConst::mini_statistics_mission_intention_list, "uri" => "/api/mini/statistics/mission_intention_list", "method" => "GET"],
                                    // 宣传比对统计
                                    ["name" => "宣传比对统计-列表", "slug" => PermissionSlugConst::mini_statistics_mission_promote_compare_list, "uri" => "/api/mini/statistics/mission_promote_compare_list", "method" => "GET"],
                                    // 政考统计
                                    ["name" => "政考统计-列表", "slug" => PermissionSlugConst::mini_statistics_mission_political_exam_list, "uri" => "/api/mini/statistics/mission_political_exam_list", "method" => "GET"],
                                    // 教育统计
                                    ["name" => "教育统计-列表", "slug" => PermissionSlugConst::mini_statistics_mission_education_list, "uri" => "/api/mini/statistics/mission_education_list", "method" => "GET"],
                                    // 完成数统计
                                    ["name" => "完成数统计-列表", "slug" => PermissionSlugConst::mini_statistics_mission_go_list, "uri" => "/api/mini/statistics/mission_go_list", "method" => "GET"],
                                    // 体检统计
                                    ["name" => "体检统计-列表", "slug" => PermissionSlugConst::mini_statistics_mission_physical_check_list, "uri" => "/api/mini/statistics/mission_physical_check_list", "method" => "GET"],
                                    // 体检复查统计
                                    ["name" => "体检复查统计-列表", "slug" => PermissionSlugConst::mini_statistics_mission_physical_recheck_list, "uri" => "/api/mini/statistics/mission_physical_recheck_list", "method" => "GET"],
                                    // 体检抽查统计
                                    ["name" => "体检抽查统计-列表", "slug" => PermissionSlugConst::mini_statistics_mission_physical_spot_check_list, "uri" => "/api/mini/statistics/mission_physical_spot_check_list", "method" => "GET"],
                                    // 任务数统计
                                    ["name" => "任务数统计-列表", "slug" => PermissionSlugConst::mini_statistics_mission_task_num_list, "uri" => "/api/mini/statistics/mission_task_num_list", "method" => "GET"],
                                    // 小红点提醒统计
                                    ["name" => "小红点提醒统计", "slug" => PermissionSlugConst::mini_statistics_remind_count, "uri" => "/api/mini/statistics/remind_count", "method" => "GET"],
                                ]
                            ]
                        ]
                    ],
                    // 其他
                    [
                        'name'  => "其他",
                        'items' => [
                            [
                                'name'  => "其他",
                                'items' => [
                                    ["name" => "文件-查看", "slug" => PermissionSlugConst::mini_file_show, "uri" => "/api/mini/get_file", "method" => "POST"],
                                    ["name" => "文件-上传", "slug" => PermissionSlugConst::mini_file_upload, "uri" => "/api/mini/upload_file", "method" => "POST"],
                                    // 导出
                                    ["name" => "导出-列表", "slug" => PermissionSlugConst::mini_task_list, "uri" => "/api/mini/task", "method" => "GET"],
                                    ["name" => "导出", "slug" => PermissionSlugConst::mini_task_store, "uri" => "/api/mini/task", "method" => "POST"],
                                    // 记录拨打电话
                                    ["name" => "记录拨打电话", "slug" => PermissionSlugConst::mini_call, "uri" => "/api/mini/call", "method" => "POST"],
                                ]
                            ]
                        ]
                    ]
                ],
            ],
        ];
    }

    public static function generatePermissionToMysql()
    {
        $clientPermissions = self::getPermission();
        foreach ($clientPermissions as $clientPermission) {
            foreach ($clientPermission['items'] as $modulePermission) {
                foreach ($modulePermission['items'] as $function) {
                    foreach ($function['items'] as $permission) {
                        echo json_encode($permission, JSON_UNESCAPED_UNICODE) . "\n";
                        Permission::query()->updateOrCreate([
                            'slug' => $permission['slug'],
                        ], [
                            'name'   => $permission['name'],
                            'slug'   => $permission['slug'],
                            'uri'    => is_array($permission['uri']) ? implode(',', $permission['uri']) : $permission['uri'],
                            'method' => $permission['method'],
                        ]);
                    }
                }
            }
        }
    }
}
