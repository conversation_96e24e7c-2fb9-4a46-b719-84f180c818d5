<?php

namespace App\Http\Services\Admin\Task\Save;

use App\Http\Consts\ImportConst;
use App\Exceptions\ErrorTrait;
use App\Http\Traits\ExcelTraits;
use App\Models\ImportItem;

class UserSavingService
{
    use ErrorTrait, ExcelTraits;

    protected $task;

    public function import($params, $task)
    {
        $this->task = $task;
        $attachmentId = $params['attachment_id'] ?? '';

        $rows = $this->readExcelFormAttachmentId($attachmentId);

        $dataItems = [];

        foreach ($rows as $index => $row) {
            if ($index == 1) {
                continue; // 跳过表头行
            }
            
            $districtName         = trim($row[1] ?? '');
            $officeName           = trim($row[2] ?? '');
            $policeDepartmentName = trim($row[3] ?? '');
            
            $hospitalName         = trim($row[5] ?? '');
            
            $streetName           = trim($row[7] ?? '');
            $schoolName           = trim($row[8] ?? '');
            $otherUnitName        = trim($row[10] ?? '');
            $personName           = trim($row[11] ?? '');
            
            $idCard               = trim($row[13] ?? '');
            $phone                = trim($row[14] ?? '');

            
            // $communityName        = trim($row[3] ?? '');
            // $companyName          = trim($row[7] ?? '');
            // $supervisionName      = trim($row[8] ?? '');
            // $jobName              = trim($row[11] ?? '');
            
            // 跳过空行
            if (empty($name) && empty($phone)) {
                continue;
            }

            $dataItems[] = [
                'name' => $name,
                'phone' => $phone,
                'email' => $email,
                'role_name' => $roleName,
            ];
        }

        $this->handleItems($dataItems);
    }

    public function handleItems($rows)
    {
        $items = [];
        foreach ($rows as $row) {
            $item = [
                "type" => ImportConst::importItemTypeUser,
                "task_id" => $this->task->id,
                "data" => json_encode($row),
                "imported" => ImportConst::importItemImportedToDo,
                "checked" => ImportConst::importItemCheckUncheck,
                "created_at" => now(),
            ];
            $items[] = $item;
        }
        ImportItem::query()->insert($items);
    }
}
