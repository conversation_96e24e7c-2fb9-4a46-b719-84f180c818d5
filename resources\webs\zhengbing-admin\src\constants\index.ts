// 合同状态枚举

export const CONTRACT_STATUS = {
  FAIL: 0,
  AUDIT_PENDING: 1,
  EXEC_PENDING: 2,
  EXECUTING: 3,
  FINISH: 4,
};

// 合同类型枚举
export const CONTRACT_TYPES = {
  MAIN: 0,
  SUB: 1,
  SUPPLEMENT: 2,
};

// 合同收付类型枚举
export const CONTRACT_PAYMENT_TYPES = {
  PAYMENT: 0,
  RECEIPT: 1,
};

// 标签类型
type TagTheme = 'default' | 'success' | 'primary' | 'warning' | 'danger';
// 通知的优先级对应的标签类型
export const NOTIFICATION_TYPES: Map<string, TagTheme> = new Map([
  ['low', 'primary'],
  ['middle', 'warning'],
  ['high', 'danger'],
]);

// 通用请求头
export enum ContentTypeEnum {
  Json = 'application/json;charset=UTF-8',
  FormURLEncoded = 'application/x-www-form-urlencoded;charset=UTF-8',
  FormData = 'multipart/form-data;charset=UTF-8',
}

export const categoryTemplateMap = {
  article: {
    status: 'article',
    text: '文章',
  },
  business: {
    status: 'business',
    text: '业务类型',
  },
  course: {
    status: 'course',
    text: '课程类型',
  },
  work_task: {
    status: 'work_task',
    text: '日常办公任务类型',
  },
};

export const articleTemplateMap = {
  article: {
    status: 'article',
    text: '文章',
  },
  consult_knowledge: {
    status: 'consult_knowledge',
    text: '咨询知识',
  },
};

export const menuTemplateMap = {
  category: {
    status: 'article-category',
    text: '文章类目',
  },
  page: {
    status: 'page',
    text: '页面',
  },
  fixed: {
    status: 'fixed',
    text: '固定菜单',
  },
  diy: {
    status: 'custom',
    text: '自定义菜单',
  },
};

export const userSexMap = {
  male: {
    status: 'male',
    text: '男',
    color: '#409EFF',
  },
  female: {
    status: 'female',
    text: '女',
    color: '#F56C6C',
  },
  unknown: {
    status: 'unknown',
    text: '未知',
    color: '#909399',
  },
};

export const userStatusMap = {
  normal: {
    status: 'normal',
    text: '正常',
    color: '#67C23A',
  },
  disabled: {
    status: 'disabled',
    text: '禁用',
    color: '#F56C6C',
  },
};

export const chapterTemplateMap = {
  chapter: {
    status: 'chapter',
    text: '章',
  },
  video: {
    status: 'video',
    text: '视频',
  },
  voice: {
    status: 'voice',
    text: '音频',
  },
  practice: {
    status: 'practice',
    text: '练习',
  },
  assessment: {
    status: 'assessment',
    text: '小结考核',
  },
};

export const fileExtensionMap = {
  pdf: {
    status: 'pdf',
    text: 'PDF',
  },
  doc: {
    status: 'doc',
    text: 'DOC',
  },
  docx: {
    status: 'docx',
    text: 'DOCX',
  },
  xls: {
    status: 'xls',
    text: 'XLS',
  },
  xlsx: {
    status: 'xlsx',
    text: 'XLSX',
  },
  ppt: {
    status: 'ppt',
    text: 'PPT',
  },
  pptx: {
    status: 'pptx',
    text: 'PPTX',
  },
  txt: {
    status: 'txt',
    text: 'TXT',
  },
  jpg: {
    status: 'jpg',
    text: 'JPG',
  },
  jpeg: {
    status: 'jpeg',
    text: 'JPEG',
  },
  png: {
    status: 'png',
    text: 'PNG',
  },
  gif: {
    status: 'gif',
    text: 'GIF',
  },
  mp4: {
    status: 'mp4',
    text: 'MP4',
  },
  avi: {
    status: 'avi',
    text: 'AVI',
  },
  mov: {
    status: 'mov',
    text: 'MOV',
  },
  mp3: {
    status: 'mp3',
    text: 'MP3',
  },
  wav: {
    status: 'wav',
    text: 'WAV',
  },
};

export const fileTypeMap = {
  image: {
    extensions: [
      fileExtensionMap.jpg.status,
      fileExtensionMap.jpeg.status,
      fileExtensionMap.png.status,
      fileExtensionMap.gif.status,
    ],
    text: '图片',
  },
  video: {
    extensions: [fileExtensionMap.mp4.status, fileExtensionMap.avi.status, fileExtensionMap.mov.status],
    text: '视频',
  },
  audio: {
    extensions: [fileExtensionMap.mp3.status, fileExtensionMap.wav.status],
    text: '音频',
  },
  document: {
    extensions: [
      fileExtensionMap.pdf.status,
      fileExtensionMap.doc.status,
      fileExtensionMap.docx.status,
      fileExtensionMap.xls.status,
      fileExtensionMap.xlsx.status,
      fileExtensionMap.ppt.status,
      fileExtensionMap.pptx.status,
      fileExtensionMap.txt.status,
    ],
    text: '文档',
  },
};

export const questionTypeMap = {
  single: {
    status: 'single',
    text: '单选题',
    color: '#409EFF',
  },
  multiple: {
    status: 'multiple',
    text: '多选题',
    color: '#E6A23C',
  },
  judge: {
    status: 'judge',
    text: '判断题',
    color: '#F56C6C',
  },
};

export const paperTypeMap = {
  exam: {
    status: 'exam',
    text: '考试',
  },
  practice: {
    status: 'practice',
    text: '练习',
  },
  assessment: {
    status: 'assessment',
    text: '小结考核',
  },
};

export const regionLevelMap = {
  city: {
    status: 'city',
    text: '市',
  },
  district: {
    status: 'district',
    text: '区',
  },
  street: {
    status: 'street',
    text: '街道/镇',
  },
  village: {
    status: 'village',
    text: '村/社区',
  },
  school: {
    status: 'school',
    text: '学校',
  },
  department: {
    status: 'department',
    text: '部门',
  },
  hospital: {
    status: 'hospital',
    text: '医院',
  },
};

export const taskTypeMap = {
  import_user_excel: {
    status: 'import_user_excel',
    text: '导入用户',
    url: false,
  },
  export_user_excel: {
    status: 'export_user_excel',
    text: '导出用户',
    url: true,
  },
  generate_invitation_code: {
    status: 'generate_invitation_code',
    text: '生成邀请码',
    url: false,
  },
  export_invitation_code_excel: {
    status: 'export_invitation_code_excel',
    text: '导出邀请码',
    url: true,
  },
  import_out_notice_task_receive_excel: {
    status: 'import_out_notice_task_receive_excel',
    text: '导入外呼任务接收人',
    url: false,
  },
  export_out_notice_task_receive_excel: {
    status: 'export_out_notice_task_receive_excel',
    text: '导出外呼任务接收人',
    url: true,
  },
  import_teen_soldier_excel: {
    status: 'import_teen_soldier_excel',
    text: '导入应征青年',
    url: false,
  },
  export_teen_soldier_excel: {
    status: 'export_teen_soldier_excel',
    text: '导出应征青年',
    url: true,
  },
  import_enlist_soldier_excel: {
    status: 'import_enlist_soldier_excel',
    text: '导入在伍士兵',
    url: false,
  },
  export_enlist_soldier_excel: {
    status: 'export_enlist_soldier_excel',
    text: '导出在伍士兵',
    url: true,
  },
  import_ex_soldier_excel: {
    status: 'import_ex_soldier_excel',
    text: '导入退役士兵',
    url: false,
  },
  export_ex_soldier_excel: {
    status: 'export_ex_soldier_excel',
    text: '导出退役士兵',
    url: true,
  },
  import_recall_task_soldier_excel: {
    status: 'import_recall_task_soldier_excel',
    text: '导入特殊时期征召任务人员',
    url: false,
  },
  export_recall_task_soldier_excel: {
    status: 'export_recall_task_soldier_excel',
    text: '导出特殊时期征召任务人员',
    url: true,
  },
  export_recall_task_soldier_link_excel: {
    status: 'export_recall_task_soldier_link_excel',
    text: '导出特殊时期征召任务人员链接',
    url: true,
  },
  import_question_excel: {
    status: 'import_question_excel',
    text: '导入题目',
    url: false,
  },
  export_question_excel: {
    status: 'export_question_excel',
    text: '导出题目',
    url: true,
  },
  import_knowledge_excel: {
    status: 'import_knowledge_excel',
    text: '导入知识点',
    url: false,
  },
  export_knowledge_excel: {
    status: 'export_knowledge_excel',
    text: '导出知识点',
    url: true,
  },
  import_consult_knowledge_excel: {
    status: 'import_consult_knowledge_excel',
    text: '导入咨询问答',
    url: false,
  },
  export_consult_knowledge_excel: {
    status: 'export_consult_knowledge_excel',
    text: '导出咨询问答',
    url: true,
  },

  // 统计数据导出
  export_statistics_department_course: {
    status: 'export_statistics_department_course',
    text: '导出部门课程统计',
    url: true,
  },
  export_statistics_user_course: {
    status: 'export_statistics_user_course',
    text: '导出人员课程统计',
    url: true,
  },
  export_statistics_course: {
    status: 'export_statistics_course',
    text: '导出课程统计',
    url: true,
  },
  export_statistics_department_paper: {
    status: 'export_statistics_department_paper',
    text: '导出部门考试统计',
    url: true,
  },
  export_statistics_user_paper: {
    status: 'export_statistics_user_paper',
    text: '导出用户考试统计',
    url: true,
  },
  export_statistics_user_point: {
    status: 'export_statistics_user_point',
    text: '导出人员积分统计',
    url: true,
  },
  export_statistics_user_call: {
    status: 'export_statistics_user_call',
    text: '导出人员拨打电话统计',
    url: true,
  },

  // 征兵人员库刷新数据
  refresh_mission_school_person_statistics_chart: {
    status: 'refresh_mission_school_person_statistics_chart',
    text: '高校人员统计图表重新统计',
    url: false,
  },
  refresh_mission_school_person_statistics_list: {
    status: 'refresh_mission_school_person_statistics_list',
    text: '高校人员统计数据重新统计',
    url: false,
  },

  refresh_mission_social_person_statistics_chart: {
    status: 'refresh_mission_social_person_statistics_chart',
    text: '社会适龄青年统计图表重新统计',
    url: false,
  },
  refresh_mission_social_person_statistics_list: {
    status: 'refresh_mission_social_person_statistics_list',
    text: '社会适龄青年统计数据重新统计',
    url: false,
  },

  // 高校人员统计导出
  export_mission_school_person_statistics_list: {
    status: 'export_mission_school_person_statistics_list',
    text: '导出高校人员统计数据',
    url: true,
  },

  // 社会适龄青年统计导出
  export_mission_social_person_statistics_list: {
    status: 'export_mission_social_person_statistics_list',
    text: '导出社会适龄青年统计数据',
    url: true,
  },

  refresh_statistics_course: {
    status: 'refresh_statistics_course',
    text: '刷新学习统计数据',
    url: false,
  },

  export_work_task_report: {
    status: 'export_work_task_report',
    text: '导出任务上报',
    url: true,
  },
  export_survey_form_excel: {
    status: 'export_survey_form_excel',
    text: '导出调查结果',
    url: true,
  },

  export_mission_person: {
    status: 'export_mission_person',
    text: '导出任务人员',
    url: true,
  },
  import_mission_physical_check_result: {
    status: 'import_mission_physical_check_result',
    text: '导入初次体检结果',
    url: false,
  },
  import_mission_physical_recheck_result: {
    status: 'import_mission_physical_recheck_result',
    text: '导入体检复查结果',
    url: false,
  },
  import_mission_physical_spot_check_result: {
    status: 'import_mission_physical_spot_check_result',
    text: '导入体检抽查结果',
    url: false,
  },
  import_mission_init: {
    status: 'import_mission_init',
    text: '导入任务初始化数据',
    url: false,
  },
  export_mission_to_intranet: {
    status: 'export_mission_to_intranet',
    text: '导出任务到内网',
    url: true,
  },
  import_mission_intranet_political_person_list: {
    status: 'import_mission_intranet_political_person_list',
    text: '导入内网政考人员列表',
    url: false,
  },
  import_mission_intranet_education_person_list: {
    status: 'import_mission_intranet_education_person_list',
    text: '导入内网教育人员列表',
  },
  import_mission_intranet_pre_store_person_list: {
    status: 'import_mission_intranet_pre_store_person_list',
    text: '导入内网预储人员列表',
  },
  import_mission_intranet_go_person_statistics_list: {
    status: 'import_mission_intranet_go_person_statistics_list',
    text: '导入内网交接起运人员列表',
    url: false,
  },
  import_school_person_excel: {
    status: 'import_school_person_excel',
    text: '导入高校人员',
    url: false,
  },
  export_school_person_excel: {
    status: 'export_school_person_excel',
    text: '导出高校人员',
    url: true,
  },
  import_social_person_excel: {
    status: 'import_social_person_excel',
    text: '导入社会适龄青年',
    url: false,
  },
  export_social_person_excel: {
    status: 'export_social_person_excel',
    text: '导出社会适龄青年',
    url: true,
  },
  import_mission_promote_compare_person_sign_excel: {
    status: 'import_mission_promote_compare_person_sign_excel',
    text: '导入报名名单',
    url: false,
  },

  mission_political_person_list: {
    status: 'mission_political_person_list',
    text: '导入内网政考数据',
    url: true,
  },

  refresh_school_person_chart: {
    status: 'refresh_school_person_chart',
    text: '刷新高校人员统计表',
    url: false,
  },
  refresh_mission_task_num_statistics_list: {
    status: 'refresh_mission_task_num_statistics_list',
    text: '刷新任务任务数统计表',
    url: false,
  },
  export_mission_task_num_statistics_list: {
    status: 'export_mission_task_num_statistics_list',
    text: '导出任务任务数统计表',
    url: true,
  },
  refresh_mission_detail_statistics: {
    status: 'refresh_mission_detail_statistics',
    text: '刷新任务详情数据',
    url: false,
  },
  export_mission_go_person_statistics_list: {
    status: 'export_mission_go_person_statistics_list',
    text: '导出起运人员统计表',
    url: true,
  },
  export_mission_education_person_list: {
    status: 'export_mission_education_person_list',
    text: '导出役前教育人员名单',
    url: true,
  },
  export_mission_pre_store_person_list: {
    status: 'export_mission_pre_store_person_list',
    text: '导出预储人员名单',
    url: true,
  },
  export_mission_intention_person_list: {
    status: 'export_mission_intention_person_list',
    text: '导出意向人员名单',
    url: true,
  },
  export_mission_physical_check_person_list: {
    status: 'export_mission_physical_check_person_list',
    text: '导出体检人员名单',
    url: true,
  },
  export_mission_physical_check_report_person_list: {
    status: 'export_mission_physical_check_report_person_list',
    text: '导出体检结果人员名单',
    url: true,
  },
  export_mission_physical_recheck_person_list: {
    status: 'export_mission_physical_recheck_person_list',
    text: '导出复查人员名单',
    url: true,
  },
  export_mission_physical_spot_check_person_list: {
    status: 'export_mission_physical_spot_check_person_list',
    text: '导出抽查人员名单',
    url: true,
  },
  export_mission_promote_compare_person_list: {
    status: 'export_mission_promote_compare_person_list',
    text: '导出宣传比对人员名单',
    url: true,
  },
  export_mission_promote_compare_intention_person_list: {
    status: 'export_mission_promote_compare_intention_person_list',
    text: '导出宣传比对意向人员名单',
    url: true,
  },
  export_mission_promote_compare_person_sign_list: {
    status: 'export_mission_promote_compare_person_sign_list',
    text: '导出宣传比对报名人员名单',
    url: true,
  },
  export_mission_political_person_list: {
    status: 'export_mission_political_person_list',
    text: '导出政考人员名单',
    url: true,
  },
  export_mission_physical_plan_record_list: {
    status: 'export_mission_physical_plan_record_list',
    text: '导出体检计划记录人员名单',
    url: true,
  },

  mission_promote_compare: {
    status: 'mission_promote_compare',
    text: '报名意向比对',
    url: false,
  },
  mission_physical_examination_add_person: {
    status: 'mission_physical_examination_add_person',
    text: '添加体检人员',
    url: false,
  },
  mission_physical_examination_recheck_add_person: {
    status: 'mission_physical_examination_recheck_add_person',
    text: '添加复查人员',
    url: false,
  },
  mission_physical_examination_gen_spot_check_list: {
    status: 'mission_physical_examination_gen_spot_check_list',
    text: '生成抽查人员名单',
    url: false,
  },
  clear_mission_sensitive_info: {
    status: 'clear_mission_sensitive_info',
    text: '清除敏感信息',
    url: false,
  },

  refresh_mission_promote_compare_statistics_list: {
    status: 'refresh_mission_promote_compare_statistics_list',
    text: '刷新宣传比对数据',
    url: false,
  },
  export_mission_promote_compare_statistics_list: {
    status: 'export_mission_promote_compare_statistics_list',
    text: '导出宣传比对数据',
    url: true,
  },
  refresh_mission_intention_statistics_list: {
    status: 'refresh_mission_intention_statistics_list',
    text: '刷新意向数据',
    url: false,
  },
  export_mission_intention_statistics_list: {
    status: 'export_mission_intention_statistics_list',
    text: '导出意向数据',
    url: true,
  },
  refresh_mission_political_exam_statistics_list: {
    status: 'refresh_mission_political_exam_statistics_list',
    text: '刷新政考数据',
    url: false,
  },
  export_mission_political_exam_statistics_list: {
    status: 'export_mission_political_exam_statistics_list',
    text: '导出政考数据',
    url: true,
  },
  refresh_mission_education_statistics_list: {
    status: 'refresh_mission_education_statistics_list',
    text: '刷新役前教育数据',
    url: false,
  },
  export_mission_education_statistics_list: {
    status: 'export_mission_education_statistics_list',
    text: '导出役前教育数据',
    url: true,
  },
  refresh_mission_physical_task_num_statistics_list: {
    status: 'refresh_mission_physical_task_num_statistics_list',
    text: '任务人数重新统计',
  },
  export_mission_physical_task_num_statistics_list: {
    status: 'export_mission_physical_task_num_statistics_list',
    text: '导出任务人数数据',
    url: true,
  },
  refresh_mission_physical_check_statistics_list: {
    status: 'refresh_mission_physical_check_statistics_list',
    text: '体检结果重新统计',
  },
  export_mission_physical_check_statistics_list: {
    status: 'export_mission_physical_check_statistics_list',
    text: '导出体检结果数据',
    url: true,
  },
  refresh_mission_physical_recheck_statistics_list: {
    status: 'refresh_mission_physical_recheck_statistics_list',
    text: '复查结果重新统计',
  },
  export_mission_physical_recheck_statistics_list: {
    status: 'export_mission_physical_recheck_statistics_list',
    text: '导出复查结果数据',
    url: true,
  },
  refresh_mission_physical_spot_check_statistics_list: {
    status: 'refresh_mission_physical_spot_check_statistics_list',
    text: '抽查结果重新统计',
    url: false,
  },
  export_mission_physical_spot_check_statistics_list: {
    status: 'export_mission_physical_spot_check_statistics_list',
    text: '导出抽查结果数据',
    url: true,
  },
  refresh_mission_pre_store_statistics_list: {
    status: 'refresh_mission_pre_store_statistics_list',
    text: '刷新预储数据',
    url: false,
  },
  export_mission_pre_store_statistics_list: {
    status: 'export_mission_pre_store_statistics_list',
    text: '导出预储数据',
    url: true,
  },
  refresh_mission_summary_statistics_list: {
    status: 'refresh_mission_summary_statistics_list',
    text: '刷新任务汇总数据',
    url: false,
  },
  export_mission_summary_statistics_list: {
    status: 'export_mission_summary_statistics_list',
    text: '导出任务汇总数据',
    url: true,
  },
  export_mission_region_settings: {
    status: 'export_mission_region_settings',
    text: '导出任务任务数设置',
    url: true,
  },
};

export const taskCategoryMap = {
  import: {
    status: 'import',
    text: '导入',
    url: false,
  },
  export: {
    status: 'export',
    text: '导出',
    url: true,
  },
  refresh: {
    status: 'refresh',
    text: '刷新',
    url: false,
  },
  others: {
    status: 'others',
    text: '其他',
  },
};

export const taskImportStatusMap = {
  pending: {
    status: 'pending',
    text: '待执行',
    color: '#9E9E9E',
  },
  parsing: {
    status: 'parsing',
    text: '解析中',
    color: '#2196F3',
  },
  parsed: {
    status: 'parsed',
    text: '解析完成',
    color: '#FFC107',
  },
  checking: {
    status: 'checking',
    text: '检查中',
    color: '#9C27B0',
  },
  checked: {
    status: 'checked',
    text: '检查完成',
    color: '#5EB6EA',
  },
  importing: {
    status: 'importing',
    text: '导入中',
    color: '#FF9800',
  },
  imported: {
    status: 'imported',
    text: '导入完成',
    color: '#4CAF50',
  },
  failed: {
    status: 'failed',
    text: '执行失败',
    color: '#F44336',
  },
};

export const importCheckedMap = {
  '-1': {
    status: -1,
    text: '未检查',
    color: '#9E9E9E',
  },
  1: {
    status: 1,
    text: '检查未通过',
    color: '#F56C6C',
  },
  2: {
    status: 2,
    text: '已检查',
    color: '#67C23A',
  },
};
export const importImportedMap = {
  '-1': {
    status: -1,
    text: '未导入',
    color: '#9E9E9E',
  },
  1: {
    status: 1,
    text: '导入失败',
    color: '#F56C6C',
  },
  2: {
    status: 2,
    text: '已导入',
    color: '#67C23A',
  },
};

export const taskStatusMap = {
  pending: {
    status: 'pending',
    text: '待执行',
    color: '#eeeeee',
  },
  running: {
    status: 'running',
    text: '执行中',
    color: '#E6A23C',
  },
  failed: {
    status: 'failed',
    text: '执行失败',
    color: '#F56C6C',
  },
  success: {
    status: 'success',
    text: '执行成功',
    color: '#67C23A',
  },
};

export const feedbackTypeMap = {
  instant: {
    status: 'instant',
    text: '即时反馈',
  },
  final: {
    status: 'final',
    text: '做完查看',
  },
};

export const consultFileUploadStatusMap = {
  CREATED: {
    status: 'CREATED',
    text: '待处理',
    color: '#cccccc',
  },
  PARSING: {
    status: 'PARSING',
    text: '解析中',
    color: '#409EFF',
  },
  PARSE_SUCCESS: {
    status: 'PARSE_SUCCESS',
    text: '解析成功',
    color: '#67C23A',
  },
  PARSE_FAIL: {
    status: 'PARSE_FAIL',
    text: '解析失败',
    color: '#F56C6C',
  },
  FAIL: {
    status: 'FAIL',
    text: '上传失败',
    color: '#F56C6C',
  },
};

export const consultFileIndexStatusMap = {
  CREATED: {
    status: 'CREATED',
    text: '待处理',
    color: '#cccccc',
  },
  RUNNING: {
    status: 'RUNNING',
    text: '索引中',
    color: '#409EFF',
  },
  FINISH: {
    status: 'FINISH',
    text: '索引完成',
    color: '#67C23A',
  },
  FAIL: {
    status: 'FAIL',
    text: '索引失败',
    color: '#F56C6C',
  },
};

export const reportStatusMap = {
  created: {
    status: 'created',
    text: '待处理',
    color: '#E6A23C',
  },
  replied: {
    status: 'replied',
    text: '已回复',
    color: '#67C23A',
  },
};

export const surveyUserTypeMap = {
  platform: {
    status: 'platform',
    text: '平台用户',
    color: '#E6A23C',
  },
  anybody: {
    status: 'anybody',
    text: '任意用户',
    color: '#409EFF',
  },
};

export const surveyStatusMap = {
  pending: {
    status: 'pending',
    text: '未开始',
    color: '#cccccc',
  },
  running: {
    status: 'running',
    text: '进行中',
    color: '#67C23A',
  },
  finished: {
    status: 'finished',
    text: '已结束',
    color: '#F56C6C',
  },
};

export const smsTemplateStatusMap = {
  0: {
    status: 0,
    text: '审核中',
    color: '#E6A23C',
  },
  1: {
    status: 1,
    text: '已通过',
    color: '#67C23A',
  },
  2: {
    status: 2,
    text: '未通过',
    color: '#F56C6C',
  },
  10: {
    status: 10,
    text: '已取消',
    color: '#cccccc',
  },
};

export const outNoticeTaskTypeMap = {
  sms: {
    status: 'sms',
    text: '短信',
  },
  vms: {
    status: 'vms',
    text: '语音通知',
  },
  sms_vms: {
    status: 'sms_vms',
    text: '短信+语音通知',
  },
  robot: {
    status: 'robot',
    text: '机器人对话',
  },
};

export const outNoticeTaskStatusMap = {
  created: {
    status: 'created',
    text: '待执行',
    color: '#E6A23C',
  },
  running: {
    status: 'running',
    text: '执行中',
    color: '#409EFF',
  },
  stopped: {
    status: 'stopped',
    text: '已停止',
    color: '#F56C6C',
  },
  finished: {
    status: 'finished',
    text: '已完成',
    color: '#67C23A',
  },
};

export const outNoticeTaskReceiveStatusMap = {
  created: {
    status: 'created',
    text: '待执行',
    color: '#E6A23C',
  },
  running: {
    status: 'running',
    text: '执行中',
    color: '#409EFF',
  },
  error: {
    status: 'error',
    text: '执行失败',
    color: '#F56C6C',
  },
  finished: {
    status: 'finished',
    text: '已完成',
    color: '#67C23A',
  },
};

export const outNoticeTaskReceiveTypeMap = {
  sms: {
    status: 'sms',
    text: '短信',
  },
  vms: {
    status: 'vms',
    text: '语音通知',
  },
  robot: {
    status: 'robot',
    text: '机器人对话',
  },
};

export const smsRuleMap = {
  phone_number2: {
    status: 'phone_number2',
    text: '电话号码',
  },
  time: {
    status: 'time',
    text: '时间',
  },
  money: {
    status: 'money',
    text: '金额',
  },
  user_nick: {
    status: 'user_nick',
    text: '用户昵称',
  },
  name: {
    status: 'name',
    text: '姓名',
  },
  unit_name: {
    status: 'unit_name',
    text: '单位名称',
  },
  address: {
    status: 'address',
    text: '地址',
  },
  license_plate_number: {
    status: 'license_plate_number',
    text: '车牌号',
  },
  tracking_number: {
    status: 'tracking_number',
    text: '运单号',
  },
  pick_up_code: {
    status: 'pick_up_code',
    text: '取件码',
  },
  other_number2: {
    status: 'other_number2',
    text: '其他号码',
  },
  link_param: {
    status: 'link_param',
    text: '链接参数',
  },
  email_address: {
    status: 'email_address',
    text: '邮箱地址',
  },
  others: {
    status: 'others',
    text: '其他',
  },
};

export const articleStatusMap = {
  published: {
    status: 'published',
    text: '已发布',
    color: '#67C23A',
  },
  draft: {
    status: 'draft',
    text: '草稿',
    color: '#cccccc',
  },
};

export const workTaskStatusMap = {
  published: {
    status: 'published',
    text: '已发布',
    color: '#67C23A',
  },
  draft: {
    status: 'draft',
    text: '草稿',
    color: '#cccccc',
  },
};

export const workInformStatusMap = {
  published: {
    status: 'published',
    text: '已发布',
    color: '#67C23A',
  },
  draft: {
    status: 'draft',
    text: '草稿',
    color: '#cccccc',
  },
};

export const systemNotificationTypeMap = {
  report_reply: {
    status: 'report_reply',
    text: '举报回复',
    color: '#409EFF',
  },
  report_new: {
    status: 'report_new',
    text: '新举报',
    color: '#409EFF',
  },
  login_abnormal: {
    status: 'login_abnormal',
    text: '登录异常',
    color: '#409EFF',
  },
};

export * from './missionConst';
export * from './personConst';
