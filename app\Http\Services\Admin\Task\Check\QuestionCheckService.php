<?php

namespace App\Http\Services\Admin\Task\Check;

use App\Http\Consts\ImportConst;
use App\Http\Consts\QuestionConst;
use App\Exceptions\ErrorTrait;
use App\Models\ImportItem;

class QuestionCheckService
{
    use ErrorTrait;

    private $task;

    public function handle($task)
    {
        $this->task = $task;

        $INSERT_BATCH_SIZE = env('INSERT_BATCH_SIZE', 100);

        $queryBuilder = ImportItem::query()
            ->where('task_id', $task->id)
            ->whereIn('checked', [
                ImportConst::importItemCheckUncheck,
                ImportConst::importItemCheckError,
            ]);

        $queryBuilder->clone()
            ->where('type', ImportConst::importItemTypeQuestion)
            ->chunkById($INSERT_BATCH_SIZE, function ($items) {
                foreach ($items as $item) {
                    $this->checkQuestion($item);
                }
            });
    }

    public function checkQuestion(ImportItem $item)
    {
        try {
            $data = $item->data;
            $errors = [];

            // 检查题目标题
            $title = trim($data['title'] ?? '');
            if (empty($title)) {
                $errors[] = '题目标题不能为空';
            } else if (mb_strlen($title) > 500) {
                $errors[] = '题目标题不能超过500个字符';
            }

            // 检查题目类型
            $type = trim($data['type'] ?? '');
            if (empty($type)) {
                $errors[] = '题目类型不能为空';
            } else {
                $typeMap = array_flip(QuestionConst::typeMap);
                if (!isset($typeMap[$type])) {
                    $errors[] = '题目类型错误：' . $type . '，有效类型：' . implode(', ', array_keys($typeMap));
                }
            }

            // 检查课程类型名称（可选）
            $courseTypeName = trim($data['course_type_name'] ?? '');
            if (!empty($courseTypeName) && mb_strlen($courseTypeName) > 100) {
                $errors[] = '课程类型名称不能超过100个字符';
            }

            // 检查知识点名称（可选）
            $knowledgeName = trim($data['knowledge_name'] ?? '');
            if (!empty($knowledgeName)) {
                $knowledgeNames = explode(',', $knowledgeName);
                foreach ($knowledgeNames as $name) {
                    $name = trim($name);
                    if (!empty($name) && mb_strlen($name) > 100) {
                        $errors[] = '知识点名称不能超过100个字符：' . $name;
                        break;
                    }
                }
            }

            // 检查题目内容（可选）
            $content = trim($data['content'] ?? '');
            if (!empty($content) && mb_strlen($content) > 2000) {
                $errors[] = '题目内容不能超过2000个字符';
            }

            // 检查答案（可选）
            $answer = trim($data['answer'] ?? '');
            if (!empty($answer) && mb_strlen($answer) > 1000) {
                $errors[] = '答案内容不能超过1000个字符';
            }

            // 更新检查状态
            if (empty($errors)) {
                $item->update([
                    'checked' => ImportConst::importItemCheckCheckAccepted,
                    'message' => '校验通过',
                    'checked_at' => now(),
                ]);
            } else {
                $item->update([
                    'checked' => ImportConst::importItemCheckError,
                    'message' => implode('; ', $errors),
                    'checked_at' => now(),
                ]);
            }

            return empty($errors);
        } catch (\Exception $e) {
            $item->update([
                'checked' => ImportConst::importItemCheckError,
                'message' => '校验异常：' . $e->getMessage(),
                'checked_at' => now(),
            ]);
            return false;
        }
    }
}
