<?php

namespace App\Http\Services\Admin\Task\Save;

use App\Http\Consts\ImportConst;
use App\Exceptions\ErrorTrait;
use App\Http\Traits\ExcelTraits;
use App\Models\ImportItem;

class OutNoticeTaskReceiveSavingService
{
    use ErrorTrait, ExcelTraits;

    protected $task;

    public function import($params, $task)
    {
        $this->task = $task;
        $attachmentId = $params['attachment_id'] ?? '';

        $rows = $this->readExcelFormAttachmentId($attachmentId);

        $dataItems = [];
        $headers = $rows[1] ?? []; // 获取表头

        foreach ($rows as $index => $row) {
            if ($index == 1) {
                continue; // 跳过表头行
            }

            // 构建行参数（表头作为键，行数据作为值）
            $rowParams = [];
            foreach ($headers as $headerIndex => $header) {
                $rowParams[$header] = $row[$headerIndex] ?? '';
            }

            $name = trim($row[1] ?? '');
            $phone = trim($row[2] ?? '');
            $type = trim($row[3] ?? '');

            // 跳过空行
            if (empty($name) && empty($phone)) {
                continue;
            }

            $dataItems[] = [
                'name' => $name,
                'phone' => $phone,
                'type' => $type,
                'row_params' => $rowParams, // 保存完整的行参数
            ];
        }

        $this->handleItems($dataItems);
    }

    public function handleItems($rows)
    {
        $items = [];
        foreach ($rows as $row) {
            $item = [
                "type" => ImportConst::importItemTypeOutNoticeTaskReceive,
                "task_id" => $this->task->id,
                "data" => json_encode($row),
                "imported" => ImportConst::importItemImportedToDo,
                "checked" => ImportConst::importItemCheckUncheck,
                "created_at" => now(),
            ];
            $items[] = $item;
        }
        ImportItem::query()->insert($items);
    }
}
