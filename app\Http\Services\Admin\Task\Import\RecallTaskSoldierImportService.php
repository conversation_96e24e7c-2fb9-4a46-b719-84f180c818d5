<?php

namespace App\Http\Services\Admin\Task\Import;

use App\Http\Consts\ImportConst;
use App\Exceptions\ErrorTrait;
use App\Models\ImportItem;
use App\Models\RecallTaskSoldier;
use App\Models\Region;
use App\Models\Task;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class RecallTaskSoldierImportService
{
    use ErrorTrait;

    protected $task;

    public function handle($task)
    {
        $this->task = $task;

        $INSERT_BATCH_SIZE = env('INSERT_BATCH_SIZE', 100);

        $queryBuilder = ImportItem::query()
            ->where('task_id', $task->id)
            ->whereIn('checked', [ImportConst::importItemCheckCheckAccepted])
            ->whereIn('imported', [
                ImportConst::importItemImportedToDo,
                ImportConst::importItemImportedError,
            ]);

        $queryBuilder->clone()
            ->where('type', ImportConst::importItemTypeRecallTaskSoldier)
            ->chunkById($INSERT_BATCH_SIZE, function ($items) {
                $this->importRecallTaskSoldier($items);
            });
    }

    private function importRecallTaskSoldier($items)
    {
        foreach ($items as $item) {
            DB::beginTransaction();
            try {
                $this->importItem($item->data);

                $item->update([
                    'imported' => ImportConst::importItemImportedDone,
                    'message' => '导入成功',
                    'imported_at' => now(),
                ]);

                DB::commit();
            } catch (\Exception $e) {
                DB::rollBack();
                $item->update([
                    'imported' => ImportConst::importItemImportedError,
                    'message' => '导入失败：' . $e->getMessage(),
                    'imported_at' => now(),
                ]);
            }
        }
    }

    protected function importItem($data)
    {
        // 获取任务ID
        $recallTaskId = $this->task->params['task_id'] ?? null;
        if (!$recallTaskId) {
            throw new \Exception('缺少征召任务ID');
        }

        $name = trim($data['name'] ?? '');
        $sex = trim($data['sex'] ?? '');
        $idCard = trim($data['id_card'] ?? '');
        $enlistmentTime = trim($data['enlistment_time'] ?? '');
        $enlistmentDistrictName = trim($data['enlistment_district_name'] ?? '');
        $enlistmentStreetName = trim($data['enlistment_street_name'] ?? '');
        $education = trim($data['education'] ?? '');
        $majorType = trim($data['major_type'] ?? '');
        $political = trim($data['political'] ?? '');
        $serviceUnit = trim($data['service_unit'] ?? '');
        $serviceUnitSecondary = trim($data['service_unit_secondary'] ?? '');
        $serviceJob = trim($data['service_job'] ?? '');
        $majorTypeSecondary = trim($data['major_type_secondary'] ?? '');
        $backboneStatus = trim($data['backbone_status'] ?? '');
        $dischargeTime = trim($data['discharge_time'] ?? '');
        $dischargeRank = trim($data['discharge_rank'] ?? '');
        $enlistmentProvince = trim($data['enlistment_province'] ?? '');
        $homeAddress = trim($data['home_address'] ?? '');
        $currentJob = trim($data['current_job'] ?? '');
        $currentMajor = trim($data['current_major'] ?? '');
        $contact = trim($data['contact'] ?? '');
        $familyContact = trim($data['family_contact'] ?? '');
        $isMarried = trim($data['is_married'] ?? '');
        $isOnlyChild = trim($data['is_only_child'] ?? '');
        $hasBirth = trim($data['has_birth'] ?? '');

        // 处理性别
        if ($sex === '男' || $sex === '1') {
            $sex = 1;
        } elseif ($sex === '女' || $sex === '2') {
            $sex = 2;
        } else {
            $sex = null;
        }

        // 处理入伍时间
        $enlistmentTimeCarbon = null;
        if (!empty($enlistmentTime)) {
            try {
                $enlistmentTimeCarbon = Carbon::parse($enlistmentTime);
            } catch (\Exception $e) {
                // 忽略时间解析错误
            }
        }

        // 处理退伍时间
        $dischargeTimeCarbon = null;
        if (!empty($dischargeTime)) {
            try {
                $dischargeTimeCarbon = Carbon::parse($dischargeTime);
            } catch (\Exception $e) {
                // 忽略时间解析错误
            }
        }

        // 查找地区信息
        $district = null;
        if (!empty($enlistmentDistrictName)) {
            $district = Region::query()
                ->where('name', 'like', '%' . $enlistmentDistrictName . '%')
                ->first();
        }

        // 处理布尔值
        $isMarriedValue = null;
        if ($isMarried === '是' || $isMarried === '1') {
            $isMarriedValue = 1;
        } elseif ($isMarried === '否' || $isMarried === '0') {
            $isMarriedValue = 0;
        }

        $isOnlyChildValue = null;
        if ($isOnlyChild === '是' || $isOnlyChild === '1') {
            $isOnlyChildValue = 1;
        } elseif ($isOnlyChild === '否' || $isOnlyChild === '0') {
            $isOnlyChildValue = 0;
        }

        $hasBirthValue = null;
        if ($hasBirth === '是' || $hasBirth === '1') {
            $hasBirthValue = 1;
        } elseif ($hasBirth === '否' || $hasBirth === '0') {
            $hasBirthValue = 0;
        }

        // 检查是否已存在
        $existingSoldier = RecallTaskSoldier::query()
            ->where('task_id', $recallTaskId)
            ->where('id_card', $idCard)
            ->first();

        if ($existingSoldier) {
            // 如果已存在，跳过创建（因为检查阶段已经验证过唯一性）
            return;
        }

        // 创建征召人员记录
        RecallTaskSoldier::query()->create([
            'task_id' => $recallTaskId,
            'name' => $name,
            'sex' => $sex,
            'id_card' => $idCard,
            'enlistment_time' => $enlistmentTimeCarbon,
            'enlistment_district_name' => $enlistmentDistrictName,
            'enlistment_district_code' => $district->code ?? null,
            'enlistment_street_name' => $enlistmentStreetName,
            'department_tree_code' => $district ? $district->tree_code : null,
            'education' => $education,
            'major_type' => $majorType,
            'political' => $political,
            'service_unit' => $serviceUnit,
            'service_unit_secondary' => $serviceUnitSecondary,
            'service_job' => $serviceJob,
            'major_type_secondary' => $majorTypeSecondary,
            'backbone_status' => $backboneStatus,
            'discharge_time' => $dischargeTimeCarbon,
            'discharge_rank' => $dischargeRank,
            'enlistment_province' => $enlistmentProvince,
            'home_address' => $homeAddress,
            'current_job' => $currentJob,
            'current_major' => $currentMajor,
            'contact' => $contact,
            'family_contact' => $familyContact,
            'is_married' => $isMarriedValue,
            'is_only_child' => $isOnlyChildValue,
            'has_birth' => $hasBirthValue,
        ]);
    }
}
