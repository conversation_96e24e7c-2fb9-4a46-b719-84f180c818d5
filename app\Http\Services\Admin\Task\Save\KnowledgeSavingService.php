<?php

namespace App\Http\Services\Admin\Task\Save;

use App\Http\Consts\ImportConst;
use App\Exceptions\ErrorTrait;
use App\Http\Traits\ExcelTraits;
use App\Models\ImportItem;

class KnowledgeSavingService
{
    use ErrorTrait, ExcelTraits;

    protected $task;

    public function import($params, $task)
    {
        $this->task = $task;
        $attachmentId = $params['attachment_id'] ?? '';

        $rows = $this->readExcelFormAttachmentId($attachmentId);

        $dataItems = [];

        foreach ($rows as $index => $row) {
            if ($index == 1) {
                continue; // 跳过表头行
            }
            
            $name = trim($row[1] ?? '');
            
            // 跳过空行
            if (empty($name)) {
                continue;
            }

            $dataItems[] = [
                'name' => $name,
            ];
        }

        $this->handleItems($dataItems);
    }

    public function handleItems($rows)
    {
        $items = [];
        foreach ($rows as $row) {
            $item = [
                "type" => ImportConst::importItemTypeKnowledge,
                "task_id" => $this->task->id,
                "data" => json_encode($row),
                "imported" => ImportConst::importItemImportedToDo,
                "checked" => ImportConst::importItemCheckUncheck,
                "created_at" => now(),
            ];
            $items[] = $item;
        }
        ImportItem::query()->insert($items);
    }
}
