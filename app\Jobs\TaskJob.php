<?php

namespace App\Jobs;

use App\Http\Consts\TaskConst;
use App\Http\Services\Admin\InvitationCodeService;
use App\Http\Services\Admin\Statistics\StatisticsTaskService;
use App\Http\Services\Admin\Task\Export\MissionPersonExportService;
use App\Http\Services\Admin\Task\Export\MissionPromoteCompareStatisticsExportService;
use App\Http\Services\Admin\Task\Export\MissionIntentionStatisticsExportService;
use App\Http\Services\Admin\Task\Export\MissionPoliticalExamStatisticsExportService;
use App\Http\Services\Admin\Task\Export\MissionEducationStatisticsExportService;
use App\Http\Services\Admin\Task\Export\MissionPreStoreStatisticsExportService;
use App\Http\Services\Admin\Task\Export\MissionTaskNumStatisticsExportService;
use App\Http\Services\Admin\Task\Export\MissionPhysicalTaskNumStatisticsExportService;
use App\Http\Services\Admin\Task\Export\MissionPhysicalCheckStatisticsExportService;
use App\Http\Services\Admin\Task\Export\MissionPhysicalRecheckStatisticsExportService;
use App\Http\Services\Admin\Task\Export\MissionPhysicalSpotCheckStatisticsExportService;
use App\Http\Services\Admin\Task\Export\MissionSummaryStatisticsExportService;
use App\Http\Services\Admin\Task\Export\MissionGoStatisticsExportService;
use App\Http\Services\Admin\Task\Export\MissionSchoolPersonStatisticsExportService;
use App\Http\Services\Admin\Task\Export\MissionSocialPersonStatisticsExportService;
use App\Http\Services\Admin\Task\Export\MissionToIntranetService;
use App\Http\Services\Admin\Task\Export\MissionEducationPersonListExportService;
use App\Http\Services\Admin\Task\Export\MissionPreStorePersonListExportService;
use App\Http\Services\Admin\Task\Export\MissionIntentionPersonListExportService;
use App\Http\Services\Admin\Task\Export\MissionPhysicalCheckPersonListExportService;
use App\Http\Services\Admin\Task\Export\MissionPhysicalCheckReportPersonListExportService;
use App\Http\Services\Admin\Task\Export\MissionPhysicalRecheckPersonListExportService;
use App\Http\Services\Admin\Task\Export\MissionPhysicalSpotCheckPersonListExportService;
use App\Http\Services\Admin\Task\Export\MissionPromoteComparePersonListExportService;
use App\Http\Services\Admin\Task\Export\MissionPromoteCompareIntentionPersonListExportService;
use App\Http\Services\Admin\Task\Export\MissionPromoteComparePersonSignListExportService;
use App\Http\Services\Admin\Task\Export\MissionPoliticalPersonListExportService;
use App\Http\Services\Admin\Task\Export\MissionPhysicalPlanRecordListExportService;
use App\Http\Services\Admin\Task\Export\MissionRegionSettingsExportService;
use App\Http\Services\Admin\Task\Other\MissionClearSensitiveInfoService;
use App\Http\Services\Admin\Task\Other\MissionPhysicalExaminationAddPersonService;
use App\Http\Services\Admin\Task\Other\MissionPhysicalExaminationGenSpotCheckListService;
use App\Http\Services\Admin\Task\Other\MissionPhysicalExaminationRecheckAddPersonService;
use App\Http\Services\Admin\Task\Other\MissionPromoteCompareService;
use App\Http\Services\Admin\Task\Refresh\MissionIntentionStatisticsCacheService;
use App\Http\Services\Admin\Task\Refresh\MissionPersonSchoolChartStatisticsCacheService;
use App\Http\Services\Admin\Task\Refresh\MissionPersonSchoolListStatisticsCacheService;
use App\Http\Services\Admin\Task\Refresh\MissionPersonSocialChartStatisticsCacheService;
use App\Http\Services\Admin\Task\Refresh\MissionPersonSocialListStatisticsCacheService;
use App\Http\Services\Admin\Task\Refresh\MissionPhysicalCheckCacheStatisticsService;
use App\Http\Services\Admin\Task\Refresh\MissionPhysicalRecheckCacheStatisticsService;
use App\Http\Services\Admin\Task\Refresh\MissionPhysicalSpotCheckCacheStatisticsService;
use App\Http\Services\Admin\Task\Refresh\MissionPhysicalTaskNumCacheStatisticsService;
use App\Http\Services\Admin\Task\Refresh\MissionPoliticalExamStatisticsCacheService;
use App\Http\Services\Admin\Task\Refresh\MissionEducationStatisticsCacheService;
use App\Http\Services\Admin\Task\Refresh\MissionPreStoreStatisticsCacheService;
use App\Http\Services\Admin\Task\Refresh\MissionTaskNumStatisticsCacheService;
use App\Http\Services\Admin\Task\Refresh\MissionDetailStatisticsCacheService;
use App\Http\Services\Admin\Task\Refresh\MissionPromoteCompareStatisticsCacheService;
use App\Http\Services\Admin\Task\Refresh\MissionSummaryStatisticsCacheService;
use App\Http\Services\Admin\Task\Save\MissionInitSavingService;
use App\Http\Services\Admin\Task\Save\MissionManyPersonListSavingService;
use App\Http\Services\Admin\Task\Save\MissionPromoteComparePersonSignSavingService;
use App\Http\Services\Admin\Task\Save\MissionPhysicalCheckResultSavingService;
use App\Http\Services\Admin\Task\Save\MissionPhysicalRecheckResultSavingService;
use App\Http\Services\Admin\Task\Save\MissionPhysicalSpotCheckResultSavingService;
use App\Http\Services\Admin\Task\Save\KnowledgeSavingService;
use App\Http\Services\Admin\Task\Save\QuestionSavingService;
use App\Http\Services\Admin\Task\Save\UserSavingService;
use App\Http\Services\Admin\Task\Save\OutNoticeTaskReceiveSavingService;
use App\Http\Services\Admin\Task\Save\ConsultKnowledgeSavingService;
use App\Http\Services\Admin\Task\Save\RecallTaskSoldierSavingService;
use App\Http\Services\Common\ExcelService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Throwable;

class TaskJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $task;

    /**
     * Create a new job instance.
     */
    public function __construct($task)
    {
        $this->task = $task;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $this->task->update([
            'status'   => TaskConst::taskStatusRunning,
            'start_at' => now(),
        ]);
        $params = $this->task->params;

        $error = "";

        $checkImport = false;

        try {
            switch ($this->task->type) {
                case TaskConst::taskTypeExportUserExcel:
                    $service = new ExcelService();
                    $service->userExport($params, $this->task);
                    break;
                case TaskConst::taskTypeImportUserExcel:
                    $checkImport = true;
                    $service = new UserSavingService();
                    $service->import($params, $this->task);
                    break;
                case TaskConst::taskTypeExportInvitationCodeExcel:
                    $service = new ExcelService();
                    $service->invitationCodeExport($params, $this->task);
                    break;
                case TaskConst::taskTypeGenerateInvitationCode;
                    $service = new InvitationCodeService();
                    $service->generate();
                    break;
                case TaskConst::taskTypeImportOutNoticeTaskReceiveExcel:
                    $checkImport = true;
                    $service = new OutNoticeTaskReceiveSavingService();
                    $service->import($params, $this->task);
                    break;
                case TaskConst::taskTypeExportOutNoticeTaskReceiveExcel:
                    $service = new ExcelService();
                    $service->outNoticeTaskReceiveExport($params, $this->task);
                    break;
                case TaskConst::taskTypeImportTeenSoldierExcel:
                    $service = new ExcelService();
                    $service->teenSoldierImport($params, $this->task);
                    break;
                case TaskConst::taskTypeExportTeenSoldierExcel:
                    $service = new ExcelService();
                    $service->teenSoldierExport($params, $this->task);
                    break;
                case TaskConst::taskTypeImportEnlistSoldierExcel:
                    $service = new ExcelService();
                    $service->enlistSoldierImport($params, $this->task);
                    break;
                case TaskConst::taskTypeExportEnlistSoldierExcel:
                    $service = new ExcelService();
                    $service->enlistSoldierExport($params, $this->task);
                    break;
                case TaskConst::taskTypeImportExSoldierExcel:
                    $service = new ExcelService();
                    $service->exSoldierImport($params, $this->task);
                    break;
                case TaskConst::taskTypeExportExSoldierExcel:
                    $service = new ExcelService();
                    $service->exSoldierExport($params, $this->task);
                    break;
                case TaskConst::taskTypeImportRecallTaskSoldierExcel:
                    $checkImport = true;
                    $service = new RecallTaskSoldierSavingService();
                    $service->import($params, $this->task);
                    break;
                case TaskConst::taskTypeExportRecallTaskSoldierExcel:
                    $service = new ExcelService();
                    $service->recallTaskSoldierExport($params, $this->task);
                    break;
                case TaskConst::taskTypeExportRecallTaskSoldierLinkExcel:
                    $service = new ExcelService();
                    $service->recallTaskSoldierLinkExport($params, $this->task);
                    break;
                case TaskConst::taskTypeExportQuestionExcel:
                    $service = new ExcelService();
                    $service->questionExport($params, $this->task);
                    break;
                case TaskConst::taskTypeImportQuestionExcel:
                    $checkImport = true;
                    $service = new QuestionSavingService();
                    $service->import($params, $this->task);
                    break;

                case TaskConst::taskTypeImportKnowledgeExcel:
                    $checkImport = true;
                    $service = new KnowledgeSavingService();
                    $service->import($params, $this->task);
                    break;
                case TaskConst::taskTypeExportKnowledgeExcel:
                    $service = new ExcelService();
                    $service->knowledgeExport($params, $this->task);
                    break;
                case TaskConst::taskTypeImportConsultKnowledgeExcel:
                    $checkImport = true;
                    $service = new ConsultKnowledgeSavingService();
                    $service->import($params, $this->task);
                    break;
                case TaskConst::taskTypeExportConsultKnowledgeExcel:
                    $service = new ExcelService();
                    $service->consultKnowledgeExport($params, $this->task);
                    break;
                case TaskConst::taskTypeExportStatisticsUserPoint:
                    $service = new ExcelService();
                    $service->statisticsUserPointExport($params, $this->task);
                    break;
                case TaskConst::taskTypeExportStatisticsUserCourse:
                    $service = new ExcelService();
                    $service->statisticsUserCourseExport($params, $this->task);
                    break;
                case TaskConst::taskTypeExportStatisticsDepartmentCourse:
                    $service = new ExcelService();
                    $service->statisticsDepartmentCourseExport($params, $this->task);
                    break;
                case TaskConst::taskTypeExportStatisticsCourse:
                    $service = new ExcelService();
                    $service->statisticsCourseExport($params, $this->task);
                    break;
                case TaskConst::taskTypeExportStatisticsUserPaper:
                    $service = new ExcelService();
                    $service->statisticsUserPaperExport($params, $this->task);
                    break;
                case TaskConst::taskTypeExportStatisticsDepartmentPaper:
                    $service = new ExcelService();
                    $service->statisticsDepartmentPaperExport($params, $this->task);
                    break;
                case TaskConst::taskTypeRefreshStatisticsCourse:
                    $service = new StatisticsTaskService();
                    $service->courseStatistics(get_config('current_stage_id'));
                    $service->userCourseStatistics(get_config('current_stage_id'));
                    break;
                case TaskConst::taskTypeExportWorkTaskReport:
                    $service = new ExcelService();
                    $service->workTaskReportExport($params, $this->task);
                    break;
                case TaskConst::taskTypeExportSurveyFormExcel:
                    $service = new ExcelService();
                    $service->surveyFormExport($params, $this->task);
                    break;
                case TaskConst::taskTypeExportStatisticsUserCall:
                    $service = new ExcelService();
                    $service->statisticsUserCallExport($params, $this->task);
                    break;
                case TaskConst::taskTypeImportMissionInit:
                    $checkImport = true;
                    $service     = new MissionInitSavingService();
                    $service->import($params, $this->task);
                    break;
                case TaskConst::taskTypeExportMissionPerson:
                    $service = new MissionPersonExportService();
                    $service->exportMissionPerson($params, $this->task);
                    break;

                case TaskConst::taskTypeImportMissionPromoteComparePersonSignExcel:
                    $checkImport = true;
                    $service     = new MissionPromoteComparePersonSignSavingService();
                    $service->import($params, $this->task);
                    break;
                case TaskConst::taskTypeMissionPromoteCompare:
                    $service = new MissionPromoteCompareService();
                    $service->compare($params, $this->task);
                    break;
                case TaskConst::taskTypeMissionPhysicalExaminationAddPerson:
                    $service = new MissionPhysicalExaminationAddPersonService();
                    $service->add($params, $this->task);
                    break;

                case TaskConst::taskTypeMissionPhysicalExaminationRecheckAddPerson:
                    $service = new MissionPhysicalExaminationRecheckAddPersonService();
                    $service->add($params, $this->task);
                    break;

                case TaskConst::taskTypeMissionPhysicalExaminationGenSpotCheckList:
                    $service = new MissionPhysicalExaminationGenSpotCheckListService();
                    $service->gen($params, $this->task);
                    break;

                case TaskConst::taskTypeMissionToIntranet:
                    $service = new MissionToIntranetService();
                    $service->toIntranet($params, $this->task);
                    break;

                case TaskConst::taskTypeMissionImportMissionIntranetEducationPersonList:
                case TaskConst::taskTypeMissionImportMissionIntranetPoliticalPersonList:
                case TaskConst::taskTypeMissionImportMissionIntranetPreStorePersonList:
                case TaskConst::taskTypeMissionImportMissionIntranetGoPersonStatisticsList:
                    $checkImport = true;
                    $service     = new MissionManyPersonListSavingService();
                    $service->import($params, $this->task);
                    break;

                case TaskConst::taskTypeMissionClearMissionSensitiveInfo:
                    $service = new MissionClearSensitiveInfoService();
                    $service->clear($params, $this->task);
                    break;

                case TaskConst::taskTypeRefreshMissionPromoteCompareStatisticsList:
                    $service = new MissionPromoteCompareStatisticsCacheService();
                    $service->refresh($params, $this->task);
                    break;

                case TaskConst::taskTypeRefreshMissionIntentionStatisticsList:
                    $service = new MissionIntentionStatisticsCacheService();
                    $service->refresh($params, $this->task);
                    break;

                case TaskConst::taskTypeRefreshMissionPoliticalExamStatisticsList:
                    $service = new MissionPoliticalExamStatisticsCacheService();
                    $service->refresh($params, $this->task);
                    break;

                case TaskConst::taskTypeRefreshMissionEducationStatisticsList:
                    $service = new MissionEducationStatisticsCacheService();
                    $service->refresh($params, $this->task);
                    break;

                case TaskConst::taskTypeRefreshMissionPhysicalTaskNumStatisticsList:
                    $service = new MissionPhysicalTaskNumCacheStatisticsService();
                    $service->refresh($params, $this->task);
                    break;

                case TaskConst::taskTypeRefreshMissionPhysicalCheckStatisticsList:
                    $service = new MissionPhysicalCheckCacheStatisticsService();
                    $service->refresh($params, $this->task);
                    break;

                case TaskConst::taskTypeRefreshMissionPhysicalRecheckStatisticsList:
                    $service = new MissionPhysicalRecheckCacheStatisticsService();
                    $service->refresh($params, $this->task);
                    break;

                case TaskConst::taskTypeRefreshMissionPhysicalSpotCheckStatisticsList:
                    $service = new MissionPhysicalSpotCheckCacheStatisticsService();
                    $service->refresh($params, $this->task);
                    break;

                case TaskConst::taskTypeRefreshMissionPreStoreStatisticsList:
                    $service = new MissionPreStoreStatisticsCacheService();
                    $service->refresh($params, $this->task);
                    break;

                case TaskConst::taskTypeRefreshMissionTaskNumStatisticsList:
                    $service = new MissionTaskNumStatisticsCacheService();
                    $service->refresh($params, $this->task);
                    break;

                case TaskConst::taskTypeRefreshMissionDetailStatistics:
                    $service = new MissionDetailStatisticsCacheService();
                    $service->refresh($params, $this->task);
                    break;

                case TaskConst::taskTypeRefreshMissionSummaryStatisticsList:
                    $service = new MissionSummaryStatisticsCacheService();
                    $service->refresh($params, $this->task);
                    break;

                // 任务人员统计
                case TaskConst::taskTypeRefreshMissionSchoolPersonStatisticsChart:
                    $service = new MissionPersonSchoolChartStatisticsCacheService();
                    $service->refresh($params, $this->task);
                    break;
                case TaskConst::taskTypeRefreshMissionSchoolPersonStatisticsList:
                    $service = new MissionPersonSchoolListStatisticsCacheService();
                    $service->refresh($params, $this->task);
                    break;
                case TaskConst::taskTypeRefreshMissionSocialPersonStatisticsChart:
                    $service = new MissionPersonSocialChartStatisticsCacheService();
                    $service->refresh($params, $this->task);
                    break;
                case TaskConst::taskTypeRefreshMissionSocialPersonStatisticsList:
                    $service = new MissionPersonSocialListStatisticsCacheService();
                    $service->refresh($params, $this->task);
                    break;

                // 导出任务统计数据
                case TaskConst::taskTypeExportMissionPromoteCompareStatisticsList:
                    $service = new MissionPromoteCompareStatisticsExportService();
                    $service->export($params, $this->task);
                    break;
                case TaskConst::taskTypeExportMissionIntentionStatisticsList:
                    $service = new MissionIntentionStatisticsExportService();
                    $service->export($params, $this->task);
                    break;
                case TaskConst::taskTypeExportMissionPoliticalExamStatisticsList:
                    $service = new MissionPoliticalExamStatisticsExportService();
                    $service->export($params, $this->task);
                    break;
                case TaskConst::taskTypeExportMissionEducationStatisticsList:
                    $service = new MissionEducationStatisticsExportService();
                    $service->export($params, $this->task);
                    break;
                case TaskConst::taskTypeExportMissionPreStoreStatisticsList:
                    $service = new MissionPreStoreStatisticsExportService();
                    $service->export($params, $this->task);
                    break;
                case TaskConst::taskTypeExportMissionTaskNumStatisticsList:
                    $service = new MissionTaskNumStatisticsExportService();
                    $service->export($params, $this->task);
                    break;
                case TaskConst::taskTypeExportMissionPhysicalTaskNumStatisticsList:
                    $service = new MissionPhysicalTaskNumStatisticsExportService();
                    $service->export($params, $this->task);
                    break;
                case TaskConst::taskTypeExportMissionPhysicalCheckStatisticsList:
                    $service = new MissionPhysicalCheckStatisticsExportService();
                    $service->export($params, $this->task);
                    break;
                case TaskConst::taskTypeExportMissionPhysicalRecheckStatisticsList:
                    $service = new MissionPhysicalRecheckStatisticsExportService();
                    $service->export($params, $this->task);
                    break;
                case TaskConst::taskTypeExportMissionPhysicalSpotCheckStatisticsList:
                    $service = new MissionPhysicalSpotCheckStatisticsExportService();
                    $service->export($params, $this->task);
                    break;
                case TaskConst::taskTypeExportMissionSummaryStatisticsList:
                    $service = new MissionSummaryStatisticsExportService();
                    $service->export($params, $this->task);
                    break;
                case TaskConst::taskTypeExportMissionGoPersonStatisticsList:
                    $service = new MissionGoStatisticsExportService();
                    $service->export($params, $this->task);
                    break;
                case TaskConst::taskTypeExportMissionSchoolPersonStatisticsList:
                    $service = new MissionSchoolPersonStatisticsExportService();
                    $service->export($params, $this->task);
                    break;
                case TaskConst::taskTypeExportMissionSocialPersonStatisticsList:
                    $service = new MissionSocialPersonStatisticsExportService();
                    $service->export($params, $this->task);
                    break;

                case TaskConst::taskTypeExportMissionEducationPersonList:
                    $service = new MissionEducationPersonListExportService();
                    $service->export($params, $this->task);
                    break;
                case TaskConst::taskTypeExportMissionPreStorePersonList:
                    $service = new MissionPreStorePersonListExportService();
                    $service->export($params, $this->task);
                    break;
                case TaskConst::taskTypeExportMissionIntentionPersonList:
                    $service = new MissionIntentionPersonListExportService();
                    $service->export($params, $this->task);
                    break;
                case TaskConst::taskTypeExportMissionPhysicalCheckPersonList:
                    $service = new MissionPhysicalCheckPersonListExportService();
                    $service->export($params, $this->task);
                    break;
                case TaskConst::taskTypeExportMissionPhysicalCheckReportPersonList:
                    $service = new MissionPhysicalCheckReportPersonListExportService();
                    $service->export($params, $this->task);
                    break;
                case TaskConst::taskTypeExportMissionPhysicalRecheckPersonList:
                    $service = new MissionPhysicalRecheckPersonListExportService();
                    $service->export($params, $this->task);
                    break;
                case TaskConst::taskTypeExportMissionPhysicalSpotCheckPersonList:
                    $service = new MissionPhysicalSpotCheckPersonListExportService();
                    $service->export($params, $this->task);
                    break;
                case TaskConst::taskTypeExportMissionPromoteComparePersonList:
                    $service = new MissionPromoteComparePersonListExportService();
                    $service->export($params, $this->task);
                    break;
                case TaskConst::taskTypeExportMissionPromoteCompareIntentionPersonList:
                    $service = new MissionPromoteCompareIntentionPersonListExportService();
                    $service->export($params, $this->task);
                    break;
                case TaskConst::taskTypeExportMissionPromoteComparePersonSignList:
                    $service = new MissionPromoteComparePersonSignListExportService();
                    $service->export($params, $this->task);
                    break;
                case TaskConst::taskTypeExportMissionPoliticalPersonList:
                    $service = new MissionPoliticalPersonListExportService();
                    $service->export($params, $this->task);
                    break;
                case TaskConst::taskTypeExportMissionPhysicalPlanRecordList:
                    $service = new MissionPhysicalPlanRecordListExportService();
                    $service->export($params, $this->task);
                    break;
                case TaskConst::taskTypeExportMissionRegionSettings:
                    $service = new MissionRegionSettingsExportService();
                    $service->export($params, $this->task);
                    break;

                case TaskConst::taskTypeImportMissionPhysicalCheckResult:
                    $checkImport = true;
                    $service     = new MissionPhysicalCheckResultSavingService();
                    $service->import($params, $this->task);
                    break;

                case TaskConst::taskTypeImportMissionPhysicalRecheckResult:
                    $checkImport = true;
                    $service     = new MissionPhysicalRecheckResultSavingService();
                    $service->import($params, $this->task);
                    break;

                case TaskConst::taskTypeImportMissionPhysicalSpotCheckResult:
                    $checkImport = true;
                    $service     = new MissionPhysicalSpotCheckResultSavingService();
                    $service->import($params, $this->task);
                    break;

                

                default:
                    $error .= "任务类型错误";
            }
        } catch (Throwable $exception) {
            $error
                .= $exception->getMessage();
            Log::error($exception->getTraceAsString());
        }

        Log::info("任务执行完成");
        if ($error == "") {
            if ($service->hasError()) {
                $error .= $service->getError();
            }
        }
        if ($error != "") {
            $this->task->update([
                'status' => TaskConst::taskStatusFailed,
                'end_at' => now(),
                'error'  => $error,
            ]);
            $this->fail();
        } else {
            if ($checkImport) {
                $this->task->update([
                    'status' => TaskConst::taskImportStatusParsed,
                    'end_at' => now(),
                ]);
                TaskCheckJob::dispatch($this->task)->afterCommit();
            } else {
                $this->task->update([
                    'status' => TaskConst::taskStatusSuccess,
                    'end_at' => now(),
                ]);
            }
        }
    }
}
