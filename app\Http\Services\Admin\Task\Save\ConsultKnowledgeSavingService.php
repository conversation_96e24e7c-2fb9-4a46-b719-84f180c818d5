<?php

namespace App\Http\Services\Admin\Task\Save;

use App\Http\Consts\ImportConst;
use App\Exceptions\ErrorTrait;
use App\Http\Traits\ExcelTraits;
use App\Models\ImportItem;

class ConsultKnowledgeSavingService
{
    use ErrorTrait, ExcelTraits;

    protected $task;

    public function import($params, $task)
    {
        $this->task = $task;
        $attachmentId = $params['attachment_id'] ?? '';

        $rows = $this->readExcelFormAttachmentId($attachmentId);

        $dataItems = [];

        foreach ($rows as $index => $row) {
            if ($index == 1) {
                continue; // 跳过表头行
            }
            
            $title = trim($row[1] ?? '');
            $content = trim($row[2] ?? '');
            
            // 跳过空行
            if (empty($title)) {
                continue;
            }

            $dataItems[] = [
                'title' => $title,
                'content' => $content,
            ];
        }

        $this->handleItems($dataItems);
    }

    protected function handleItems($dataItems)
    {
        foreach ($dataItems as $data) {
            ImportItem::query()->create([
                'task_id' => $this->task->id,
                'type' => ImportConst::importItemTypeConsultKnowledge,
                'data' => $data,
                'checked' => ImportConst::importItemCheckUncheck,
                'imported' => ImportConst::importItemImportedToDo,
            ]);
        }
    }
}
