<?php

namespace App\Http\Services\Admin\Task\Save;

use App\Http\Consts\ImportConst;
use App\Exceptions\ErrorTrait;
use App\Http\Traits\ExcelTraits;
use App\Models\ImportItem;

class QuestionSavingService
{
    use ErrorTrait, ExcelTraits;

    protected $task;

    public function import($params, $task)
    {
        $this->task = $task;
        $attachmentId = $params['attachment_id'] ?? '';

        $rows = $this->readExcelFormAttachmentId($attachmentId);

        $dataItems = [];

        foreach ($rows as $index => $row) {
            if ($index == 1) {
                continue; // 跳过表头行
            }
            
            $title = trim($row[1] ?? '');
            $type = trim($row[2] ?? '');
            $knowledgeName = trim($row[3] ?? '');
            $courseTypeName = trim($row[4] ?? '');
            $analysis = trim($row[5] ?? '');
            $rightAnswer = trim($row[6] ?? '');

            // 读取答案选项（从第7列开始）
            $answerIndex = 7;
            $answerOptions = [];
            while (isset($row[$answerIndex]) && !empty(trim($row[$answerIndex]))) {
                $answerOptions[] = trim($row[$answerIndex]);
                $answerIndex++;
            }

            // 跳过空行
            if (empty($title)) {
                continue;
            }

            $dataItems[] = [
                'title' => $title,
                'type' => $type,
                'knowledge_name' => $knowledgeName,
                'course_type_name' => $courseTypeName,
                'analysis' => $analysis,
                'right_answer' => $rightAnswer,
                'answer_options' => $answerOptions,
            ];
        }

        $this->handleItems($dataItems);
    }

    public function handleItems($rows)
    {
        $items = [];
        foreach ($rows as $row) {
            $item = [
                "type" => ImportConst::importItemTypeQuestion,
                "task_id" => $this->task->id,
                "data" => json_encode($row),
                "imported" => ImportConst::importItemImportedToDo,
                "checked" => ImportConst::importItemCheckUncheck,
                "created_at" => now(),
            ];
            $items[] = $item;
        }
        ImportItem::query()->insert($items);
    }
}
