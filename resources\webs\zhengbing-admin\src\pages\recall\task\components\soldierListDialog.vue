<template>
  <div>
    <t-dialog
      :header="''"
      width="80%"
      :visible="visible"
      :destroy-on-close="true"
      :cancel-btn="'关闭'"
      :confirm-btn="null"
      @close="close"
      @cancel="close"
    >
      <div class="list-common-table">
        <t-form ref="form" :data="formData" label-align="left" :label-width="60" @reset="onReset" @submit="onSearch">
          <t-row :gutter="[24, 24]">
            <t-col :md="3" :sm="6">
              <t-form-item label="姓名" name="name">
                <t-input
                  v-model="formData.name"
                  :clearable="true"
                  class="form-item-content"
                  type="search"
                  placeholder="请输入姓名"
                />
              </t-form-item>
            </t-col>
            <!--人脸识别-->
            <t-col :md="3" :sm="6">
              <t-form-item label="人脸识别" :label-width="80" name="face_complete">
                <t-select v-model="formData.face_complete" :clearable="true">
                  <t-option :value="1" label="完成"></t-option>
                  <t-option :value="0" label="未完成"></t-option>
                </t-select>
              </t-form-item>
            </t-col>
            <!--体检是否完成-->
            <t-col :md="3" :sm="6">
              <t-form-item label="体检" name="body_examination_complete">
                <t-select v-model="formData.body_examination_complete" :clearable="true">
                  <t-option :value="1" label="完成"></t-option>
                  <t-option :value="0" label="未完成"></t-option>
                </t-select>
              </t-form-item>
            </t-col>
            <!--政考是否完成-->
            <t-col :md="3" :sm="6">
              <t-form-item label="政考" name="political_examination_complete">
                <t-select v-model="formData.political_examination_complete" :clearable="true">
                  <t-option :value="1" label="完成"></t-option>
                  <t-option :value="0" label="未完成"></t-option>
                </t-select>
              </t-form-item>
            </t-col>
            <!--起运是否完成-->
            <t-col :md="3" :sm="6">
              <t-form-item label="起运" name="transport_complete">
                <t-select v-model="formData.transport_complete" :clearable="true">
                  <t-option :value="1" label="完成"></t-option>
                  <t-option :value="0" label="未完成"></t-option>
                </t-select>
              </t-form-item>
            </t-col>

            <t-col :span="8" class="operation-container">
              <t-button theme="primary" type="submit"> 查询 </t-button>
              <!--导出-->
              <export-button
                :form-data="{ ...formData, task_id: props.task.id }"
                :task-type="taskTypeMap.export_recall_task_soldier_excel.status"
              ></export-button>

              <!--导出-->
              <t-tooltip content="重新导出会刷新链接">
                <export-button
                  :btn-title="'导出链接'"
                  :form-data="{ ...formData, task_id: props.task.id }"
                  :task-type="taskTypeMap.export_recall_task_soldier_link_excel.status"
                ></export-button>
              </t-tooltip>

              <t-button type="reset" variant="base" theme="default"> 重置</t-button>
            </t-col>
          </t-row>
        </t-form>

        <t-space style="margin-top: 16px">
          <t-button theme="primary" @click="handleClickAdd">
            <template #icon>
              <t-icon name="add" />
            </template>
            新建
          </t-button>
          <!--导入-->
          <import-button
            :task-type="taskTypeMap.import_recall_task_soldier_excel.status"
            :template-url="'特殊时期征召人员导入模板.xlsx'"
            :params="{ task_id: task.id }"
            @close="fetchData"
          />
          <t-popconfirm
            v-if="selectedRowKeys.length > 0"
            content="确认删除吗"
            @confirm="handleClickDelete(selectedRowKeys)"
          >
            <t-button theme="danger">
              <template #icon>
                <t-icon name="delete" />
              </template>
              批量删除
            </t-button>
          </t-popconfirm>
          <!--清空-->
          <t-popconfirm content="确认清空吗" @confirm="handleClickClear()">
            <t-button theme="danger">
              <template #icon>
                <t-icon name="delete" />
              </template>
              清空
            </t-button>
          </t-popconfirm>
        </t-space>

        <div class="table-container">
          <t-table
            :select-on-row-click="true"
            :data="list"
            :columns="COLUMNS"
            :row-key="rowKey"
            :vertical-align="verticalAlign"
            :hover="hover"
            :pagination="pagination"
            :loading="dataLoading"
            :header-affixed-top="headerAffixedTop"
            :selected-row-keys="selectedRowKeys"
            @page-change="rehandlePageChange"
            @change="rehandleChange"
            @select-change="rehandleSelectChange"
          >
            <!--性别-->
            <template #sex="slotProps">
              <t-tag variant="light">
                {{ slotProps.row.sex }}
              </t-tag>
            </template>
            <!--人脸识别是否成功-->
            <template #face_complete="{ row }">
              <t-tag variant="light" :theme="row.face_complete ? 'success' : 'danger'">
                {{ row.face_complete ? '完成' : '待完成' }}
              </t-tag>
            </template>
            <!--体检是否完成-->
            <template #body_examination_complete="{ row }">
              <t-tag variant="light" :theme="row.body_examination_complete ? 'success' : 'danger'">
                {{ row.body_examination_complete_at ? '完成' : '待完成' }}
              </t-tag>
            </template>
            <!--政考是否完成-->
            <template #political_examination_complete="{ row }">
              <t-tag variant="light" :theme="row.political_examination_complete ? 'success' : 'danger'">
                {{ row.political_examination_complete ? '完成' : '待完成' }}
              </t-tag>
            </template>
            <!--起运是否完成-->
            <template #transport_complete="{ row }">
              <t-tag variant="light" :theme="row.transport_complete ? 'success' : 'danger'">
                {{ row.transport_complete ? '完成' : '待完成' }}
              </t-tag>
            </template>
            <template #op="slotProps">
              <t-space @click.stop>
                <t-link theme="primary" @click="handleClickEdit(slotProps)"> 修改</t-link>
                <t-popconfirm content="确认删除吗" @confirm="handleClickDelete([slotProps.row.id])">
                  <t-link theme="danger"> 删除</t-link>
                </t-popconfirm>
              </t-space>
            </template>
          </t-table>
        </div>
      </div>
    </t-dialog>

    <recall-soldier-create-dialog
      :visible="createDialogVisible"
      :row="operateRow"
      :task="task"
      @close="closeCreateDialog"
      @success="createSuccess"
    ></recall-soldier-create-dialog>
  </div>
</template>
<script setup lang="tsx">
import { PageInfo, PrimaryTableCol, TableProps, TableRowData } from 'tdesign-vue-next';
import { computed, onMounted, ref, watch } from 'vue';

import {
  clearRecallTaskSoldier,
  deleteRecallTaskSoldier,
  getRecallTaskSoldierList,
} from '@/api/recall/recallTaskSoldier';
import ImportButton from '@/components/task/excel/importButton.vue';
import ExportButton from '@/components/task/exportButton.vue';
import { prefix } from '@/config/global';
import { taskTypeMap } from '@/constants';
import RecallSoldierCreateDialog from '@/pages/recall/task/components/recallSoldierCreateDialog.vue';
import { useSettingStore } from '@/store';

const store = useSettingStore();

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  task: {
    type: Object,
    default: () => {
      return {};
    },
  },
});

const emit = defineEmits(['close']);
onMounted(() => {});

interface FormData {
  name: string;
  status: string;
  face_complete: string;
  body_examination_complete: string;
  political_examination_complete: string;
  transport_complete: string;
}

const COLUMNS: PrimaryTableCol[] = [
  {
    colKey: 'row-select',
    type: 'multiple',
    align: 'left',
  },
  {
    title: '名称',
    colKey: 'name',
    width: 100,
    fixed: 'left',
  },
  // 性别
  {
    title: '性别',
    colKey: 'sex',
    width: 80,
  },
  // id_card
  {
    title: '身份证号',
    colKey: 'id_card',
    width: 200,
  },
  // nation
  {
    title: '入伍时间',
    colKey: 'enlistment_time',
    align: 'left',
    width: 100,
  },
  // 入伍所在区
  {
    title: '入伍所在区',
    colKey: 'enlistment_district_name',
    width: 100,
  },
  // 入伍街道
  {
    title: '入伍街道（院校）',
    colKey: 'enlistment_street_name',
    width: 100,
  },
  // 文化程度
  {
    title: '文化程度',
    colKey: 'education',
    align: 'left',
    width: 150,
  },
  // 专业类型
  {
    title: '专业类型',
    colKey: 'major_type',
    align: 'left',
    width: 150,
  },
  // 政治面貌
  {
    title: '政治面貌',
    colKey: 'political',
    align: 'left',
    width: 150,
    ellipsis: true,
  },
  // 服役单位
  {
    title: '服役单位',
    colKey: 'service_unit',
    align: 'left',
    width: 150,
  },
  // 服役二级单位
  {
    title: '服役二级单位',
    colKey: 'service_unit_secondary',
    align: 'left',
    width: 150,
  },
  // service_job
  {
    title: '专业岗位',
    colKey: 'service_job',
    align: 'left',
    width: 150,
  },
  // service_job_category
  {
    title: '专业类别',
    colKey: 'service_job_category',
    align: 'left',
    width: 150,
  },
  // 任骨干情况
  {
    title: '任骨干情况',
    colKey: 'service_backbone',
    align: 'left',
    width: 150,
  },
  // 退役时间
  {
    title: '退役时间',
    colKey: 'retirement_time',
    align: 'left',
    width: 100,
  },
  // 退役时军衔
  {
    title: '退役时军衔',
    colKey: 'retirement_rank',
    align: 'left',
    width: 150,
  },
  // 入伍前省份
  {
    title: '入伍前省份',
    colKey: 'enlistment_province',
    width: 100,
  },
  // 家庭地址
  {
    title: '家庭地址',
    colKey: 'family_address',
    width: 150,
    ellipsis: true,
  },
  // 现工作单位
  {
    title: '现工作单位',
    colKey: 'now_working_unit',
    width: 150,
    ellipsis: true,
  },
  // 现从事专业
  {
    title: '现从事专业',
    colKey: 'now_major',
    width: 150,
    ellipsis: true,
  },

  // 联系方式
  {
    title: '联系方式',
    colKey: 'contact',
    width: 150,
  },
  // 家庭成员联系方式
  {
    title: '家庭成员联系方式',
    colKey: 'family_contact',
    width: 150,
  },
  // 是否结婚
  {
    title: '是否结婚',
    colKey: 'married',
    align: 'left',
    width: 150,
  },
  // 是否独生子
  {
    title: '是否独生子',
    colKey: 'single',
    align: 'left',
    width: 150,
  },
  // 是否生育
  {
    title: '是否生育',
    colKey: 'birth',
    align: 'left',
    width: 150,
  },
  // 人脸识别是否成功
  {
    title: '人脸识别是否成功',
    colKey: 'face_complete',
    align: 'left',
    width: 150,
  },
  // 人脸识别完成时间
  {
    title: '人脸识别完成时间',
    colKey: 'face_complete_at',
    align: 'left',
    width: 150,
  },
  // 体检是否完成
  {
    title: '体检是否完成',
    colKey: 'body_examination_complete',
    align: 'left',
    width: 150,
  },
  // 体检完成时间
  {
    title: '体检完成时间',
    colKey: 'body_examination_complete_at',
    align: 'left',
    width: 150,
  },
  // 政考是否完成
  {
    title: '政考是否完成',
    colKey: 'political_examination_complete',
    align: 'left',
    width: 150,
  },
  // 政考完成时间
  {
    title: '政考完成时间',
    colKey: 'political_examination_complete_at',
    align: 'left',
    width: 150,
  },
  // 起运是否完成
  {
    title: '起运是否完成',
    colKey: 'transport_complete',
    align: 'left',
    width: 150,
  },
  // 起运完成时间
  {
    title: '起运完成时间',
    colKey: 'transport_complete_at',
    align: 'left',
    width: 150,
  },
  // 备注
  {
    align: 'left',
    fixed: 'right',
    width: 160,
    colKey: 'op',
    title: '操作',
  },
];

const searchFormInit = {
  name: '',
  status: '',
  face_complete: '',
  body_examination_complete: '',
  political_examination_complete: '',
  transport_complete: '',
};

const formData = ref<FormData>({ ...searchFormInit });
const rowKey = 'id';
const verticalAlign = 'top' as const;
const hover = true;

const pagination = ref({
  pageSize: 10,
  total: 100,
  current: 1,
});

const list = ref([]);

const dataLoading = ref(false);

onMounted(() => {});

watch(
  () => props.visible,
  (val) => {
    if (val) {
      fetchData();
    }
  },
);

const fetchData = async () => {
  dataLoading.value = true;
  try {
    const query = {
      ...formData.value,
      page: pagination.value.current,
      page_size: pagination.value.pageSize,
      task_id: props.task.id,
    };
    const { data, total, current_page, per_page } = await getRecallTaskSoldierList(query);
    list.value = data;
    pagination.value = {
      total,
      current: current_page,
      pageSize: parseInt(String(per_page), 10),
    };
  } catch (e) {
    console.log(e);
  } finally {
    dataLoading.value = false;
  }
};

// 多选
const selectedRowKeys = ref<TableProps['selectedRowKeys']>([]);
const rehandleSelectChange: TableProps['onSelectChange'] = (value, ctx) => {
  selectedRowKeys.value = value;
  console.log(value, ctx);
};

// 搜索
const onReset = (val: unknown) => {
  console.log(val);
  formData.value = { ...searchFormInit };
};
const onSearch = (val: unknown) => {
  console.log(val);
  console.log(formData.value);
  fetchData();
};
const rehandlePageChange = (pageInfo: PageInfo, newDataSource: TableRowData[]) => {
  console.log('分页变化', pageInfo, newDataSource);
  pagination.value.pageSize = pageInfo.pageSize;
  pagination.value.current = pageInfo.current;
  fetchData();
};
const rehandleChange = (changeParams: unknown, triggerAndData: unknown) => {
  console.log('统一Change', changeParams, triggerAndData);
};

const headerAffixedTop = computed(
  () =>
    ({
      offsetTop: store.isUseTabsRouter ? 48 : 0,
      container: `.${prefix}-layout`,
    }) as any, // TO BE FIXED
);

const close = () => {
  emit('close');
};

const handleClickDelete = (ids) => {
  console.log(ids);
  deleteRecallTaskSoldier({
    ids,
  }).then((res) => {
    console.log(res);
    fetchData();
  });
};

const createDialogVisible = ref(false);
const operateRow = ref({});
const handleClickEdit = async ({ row }: any) => {
  operateRow.value = row;
  createDialogVisible.value = true;
};

const handleClickAdd = () => {
  createDialogVisible.value = true;
};

const closeCreateDialog = () => {
  createDialogVisible.value = false;
  operateRow.value = {};
};
const createSuccess = () => {
  fetchData();
  createDialogVisible.value = false;
  operateRow.value = {};
};

const handleClickClear = () => {
  clearRecallTaskSoldier({
    task_id: props.task.id,
  }).then((res) => {
    console.log(res);
    fetchData();
  });
};
</script>
<style scoped lang="less"></style>
